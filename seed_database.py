#!/usr/bin/env python3
"""
Comprehensive Database Seeding Script for Allora Sustainable Marketplace
========================================================================

This script seeds ALL columns of ALL main tables with appropriate data:
- Categories (with all fields including images, meta data, etc.)
- 50 sustainable products with realistic names and complete data
- Product images for each product
- Users with complete profiles and images
- Sellers with complete business information
- Orders with all financial and tracking data
- Order items with product snapshots
- Product reviews with ratings and comments
- Admin users with permissions
- Banners with images and scheduling
- Inventory logs and activity tracking

Every single column in each table is populated with realistic, appropriate data.

Usage:
    python seed_database.py

Author: Allora Development Team
Date: 2025-07-17
"""

import os
import sys
import random
import hashlib
import uuid
from datetime import datetime, timedelta, date
from decimal import Decimal

# Add the current directory to Python path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Flask app and models
from app import (
    app, db, Category, Product, ProductImage, Banner, AdminUser,
    User, Order, OrderItem, ProductReview, Seller, InventoryLog,
    AdminActivityLog, UserInteractionLog, UserBehaviorProfile
)

def generate_password_hash(password):
    """Generate a simple password hash for demo purposes"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_admin_users():
    """Create admin users with all fields populated"""
    print("👨‍💼 Creating admin users...")
    
    admin_users_data = [
        {
            'username': 'super_admin',
            'email': '<EMAIL>',
            'password': 'admin123',
            'first_name': 'John',
            'last_name': 'Administrator',
            'role': 'super_admin',
            'can_manage_products': True,
            'can_manage_orders': True,
            'can_manage_users': True,
            'can_view_analytics': True,
            'can_manage_content': True,
            'can_manage_inventory': True,
            'is_active': True,
            'last_login': datetime.now() - timedelta(hours=2)
        },
        {
            'username': 'product_manager',
            'email': '<EMAIL>',
            'password': 'manager123',
            'first_name': 'Sarah',
            'last_name': 'ProductManager',
            'role': 'admin',
            'can_manage_products': True,
            'can_manage_orders': True,
            'can_manage_users': False,
            'can_view_analytics': True,
            'can_manage_content': True,
            'can_manage_inventory': True,
            'is_active': True,
            'last_login': datetime.now() - timedelta(hours=5)
        },
        {
            'username': 'content_admin',
            'email': '<EMAIL>',
            'password': 'content123',
            'first_name': 'Mike',
            'last_name': 'ContentAdmin',
            'role': 'manager',
            'can_manage_products': False,
            'can_manage_orders': False,
            'can_manage_users': False,
            'can_view_analytics': True,
            'can_manage_content': True,
            'can_manage_inventory': False,
            'is_active': True,
            'last_login': datetime.now() - timedelta(days=1)
        }
    ]
    
    created_admins = []
    
    for admin_data in admin_users_data:
        # Check if admin already exists
        existing_admin = AdminUser.query.filter_by(email=admin_data['email']).first()
        if existing_admin:
            created_admins.append(existing_admin)
            continue
        
        admin = AdminUser(
            username=admin_data['username'],
            email=admin_data['email'],
            password=generate_password_hash(admin_data['password']),
            first_name=admin_data['first_name'],
            last_name=admin_data['last_name'],
            role=admin_data['role'],
            can_manage_products=admin_data['can_manage_products'],
            can_manage_orders=admin_data['can_manage_orders'],
            can_manage_users=admin_data['can_manage_users'],
            can_view_analytics=admin_data['can_view_analytics'],
            can_manage_content=admin_data['can_manage_content'],
            can_manage_inventory=admin_data['can_manage_inventory'],
            is_active=admin_data['is_active'],
            last_login=admin_data['last_login'],
            created_at=datetime.now() - timedelta(days=random.randint(30, 365)),
            created_by=None  # First admin has no creator
        )
        
        db.session.add(admin)
        created_admins.append(admin)
    
    db.session.commit()
    
    # Update created_by for subsequent admins
    if len(created_admins) > 1:
        for i in range(1, len(created_admins)):
            created_admins[i].created_by = created_admins[0].id
        db.session.commit()
    
    print(f"✅ Created {len(created_admins)} admin users")
    return created_admins

def create_categories():
    """Create hierarchical category structure with ALL fields populated"""
    print("🌱 Creating sustainable product categories...")
    
    # Parent categories with complete data
    parent_categories_data = [
        {
            'name': 'Home & Living',
            'slug': 'home-living',
            'description': 'Sustainable home essentials and eco-friendly living products for a greener lifestyle',
            'level': 0,
            'sort_order': 1,
            'image_url': '/static/images/categories/home-living-hero.jpg',
            'icon_class': 'fas fa-home',
            'meta_title': 'Sustainable Home & Living Products | Eco-Friendly Home Essentials',
            'meta_description': 'Discover sustainable home and living products. From eco-friendly kitchenware to natural cleaning supplies, create a greener home.',
            'is_active': True,
            'is_featured': True
        },
        {
            'name': 'Personal Care',
            'slug': 'personal-care',
            'description': 'Natural and organic personal care products for healthy skin, hair, and body',
            'level': 0,
            'sort_order': 2,
            'image_url': '/static/images/categories/personal-care-hero.jpg',
            'icon_class': 'fas fa-spa',
            'meta_title': 'Natural Personal Care Products | Organic Beauty & Skincare',
            'meta_description': 'Shop natural personal care products. Organic skincare, chemical-free cosmetics, and eco-friendly beauty essentials.',
            'is_active': True,
            'is_featured': True
        },
        {
            'name': 'Fashion & Accessories',
            'slug': 'fashion-accessories',
            'description': 'Sustainable fashion and eco-friendly accessories made from ethical materials',
            'level': 0,
            'sort_order': 3,
            'image_url': '/static/images/categories/fashion-hero.jpg',
            'icon_class': 'fas fa-tshirt',
            'meta_title': 'Sustainable Fashion & Eco-Friendly Accessories | Ethical Clothing',
            'meta_description': 'Discover sustainable fashion and eco-friendly accessories. Ethically made clothing, organic fabrics, and sustainable style.',
            'is_active': True,
            'is_featured': True
        },
        {
            'name': 'Food & Beverages',
            'slug': 'food-beverages',
            'description': 'Organic and sustainable food products, natural beverages, and healthy snacks',
            'level': 0,
            'sort_order': 4,
            'image_url': '/static/images/categories/food-beverages-hero.jpg',
            'icon_class': 'fas fa-apple-alt',
            'meta_title': 'Organic Food & Natural Beverages | Sustainable Nutrition',
            'meta_description': 'Shop organic food and natural beverages. Sustainable nutrition, healthy snacks, and eco-friendly food products.',
            'is_active': True,
            'is_featured': True
        },
        {
            'name': 'Baby & Kids',
            'slug': 'baby-kids',
            'description': 'Safe and sustainable products for babies and children, made with natural materials',
            'level': 0,
            'sort_order': 5,
            'image_url': '/static/images/categories/baby-kids-hero.jpg',
            'icon_class': 'fas fa-baby',
            'meta_title': 'Safe Baby & Kids Products | Natural Children\'s Items',
            'meta_description': 'Safe and sustainable baby and kids products. Natural toys, organic clothing, and eco-friendly children\'s essentials.',
            'is_active': True,
            'is_featured': True
        }
    ]
    
    created_categories = {}
    
    # Create parent categories first
    for cat_data in parent_categories_data:
        category = Category(
            name=cat_data['name'],
            slug=cat_data['slug'],
            description=cat_data['description'],
            parent_id=None,
            level=cat_data['level'],
            sort_order=cat_data['sort_order'],
            image_url=cat_data['image_url'],
            icon_class=cat_data['icon_class'],
            meta_title=cat_data['meta_title'],
            meta_description=cat_data['meta_description'],
            is_active=cat_data['is_active'],
            is_featured=cat_data['is_featured'],
            product_count=0,  # Will be updated later
            created_at=datetime.now() - timedelta(days=random.randint(60, 180)),
            updated_at=datetime.now() - timedelta(days=random.randint(1, 30))
        )
        db.session.add(category)
        created_categories[cat_data['slug']] = category
    
    db.session.commit()

    # Child categories with complete data
    child_categories_data = [
        # Home & Living children
        {
            'name': 'Kitchen & Dining',
            'slug': 'kitchen-dining',
            'parent_slug': 'home-living',
            'description': 'Sustainable kitchenware, dining essentials, and eco-friendly cooking tools',
            'image_url': '/static/images/categories/kitchen-dining.jpg',
            'icon_class': 'fas fa-utensils',
            'meta_title': 'Sustainable Kitchen & Dining Products | Eco-Friendly Kitchenware',
            'meta_description': 'Shop sustainable kitchen and dining products. Eco-friendly cookware, bamboo utensils, and green kitchen essentials.'
        },
        {
            'name': 'Bathroom Essentials',
            'slug': 'bathroom-essentials',
            'parent_slug': 'home-living',
            'description': 'Natural bathroom products and eco-friendly personal hygiene essentials',
            'image_url': '/static/images/categories/bathroom-essentials.jpg',
            'icon_class': 'fas fa-bath',
            'meta_title': 'Natural Bathroom Essentials | Eco-Friendly Hygiene Products',
            'meta_description': 'Natural bathroom essentials and eco-friendly hygiene products. Sustainable personal care for your daily routine.'
        },
        {
            'name': 'Home Decor',
            'slug': 'home-decor',
            'parent_slug': 'home-living',
            'description': 'Sustainable home decoration and eco-friendly furnishing accessories',
            'image_url': '/static/images/categories/home-decor.jpg',
            'icon_class': 'fas fa-couch',
            'meta_title': 'Sustainable Home Decor | Eco-Friendly Furnishing',
            'meta_description': 'Sustainable home decor and eco-friendly furnishing. Natural materials, ethical design, and green home accessories.'
        },
        {
            'name': 'Cleaning Supplies',
            'slug': 'cleaning-supplies',
            'parent_slug': 'home-living',
            'description': 'Natural and biodegradable cleaning products for a chemical-free home',
            'image_url': '/static/images/categories/cleaning-supplies.jpg',
            'icon_class': 'fas fa-spray-can',
            'meta_title': 'Natural Cleaning Supplies | Eco-Friendly Cleaning Products',
            'meta_description': 'Natural cleaning supplies and eco-friendly cleaning products. Chemical-free solutions for a healthier home.'
        },
        # Personal Care children
        {
            'name': 'Skincare',
            'slug': 'skincare',
            'parent_slug': 'personal-care',
            'description': 'Natural skincare and organic beauty products for healthy, glowing skin',
            'image_url': '/static/images/categories/skincare.jpg',
            'icon_class': 'fas fa-leaf',
            'meta_title': 'Natural Skincare Products | Organic Beauty & Cosmetics',
            'meta_description': 'Natural skincare and organic beauty products. Chemical-free cosmetics for healthy, radiant skin.'
        },
        {
            'name': 'Hair Care',
            'slug': 'hair-care',
            'parent_slug': 'personal-care',
            'description': 'Organic hair care and natural styling products for all hair types',
            'image_url': '/static/images/categories/hair-care.jpg',
            'icon_class': 'fas fa-cut',
            'meta_title': 'Organic Hair Care Products | Natural Hair Styling',
            'meta_description': 'Organic hair care and natural styling products. Sulfate-free shampoos, natural conditioners, and eco-friendly hair tools.'
        },
        {
            'name': 'Oral Care',
            'slug': 'oral-care',
            'parent_slug': 'personal-care',
            'description': 'Natural dental and oral hygiene products for healthy teeth and gums',
            'image_url': '/static/images/categories/oral-care.jpg',
            'icon_class': 'fas fa-tooth',
            'meta_title': 'Natural Oral Care Products | Eco-Friendly Dental Hygiene',
            'meta_description': 'Natural oral care and eco-friendly dental hygiene products. Chemical-free toothpaste, bamboo toothbrushes, and natural mouthwash.'
        },
        {
            'name': 'Body Care',
            'slug': 'body-care',
            'parent_slug': 'personal-care',
            'description': 'Natural body care and wellness products for complete body health',
            'image_url': '/static/images/categories/body-care.jpg',
            'icon_class': 'fas fa-heart',
            'meta_title': 'Natural Body Care Products | Organic Wellness',
            'meta_description': 'Natural body care and organic wellness products. Chemical-free lotions, natural soaps, and eco-friendly body essentials.'
        },
        # Fashion & Accessories children
        {
            'name': 'Clothing',
            'slug': 'clothing',
            'parent_slug': 'fashion-accessories',
            'description': 'Sustainable and ethically made clothing from organic and recycled materials',
            'image_url': '/static/images/categories/clothing.jpg',
            'icon_class': 'fas fa-tshirt',
            'meta_title': 'Sustainable Clothing | Ethical Fashion & Organic Apparel',
            'meta_description': 'Sustainable clothing and ethical fashion. Organic cotton, hemp, and recycled materials for eco-conscious style.'
        },
        {
            'name': 'Bags & Accessories',
            'slug': 'bags-accessories',
            'parent_slug': 'fashion-accessories',
            'description': 'Sustainable bags and eco-friendly fashion accessories',
            'image_url': '/static/images/categories/bags-accessories.jpg',
            'icon_class': 'fas fa-shopping-bag',
            'meta_title': 'Sustainable Bags & Eco-Friendly Accessories',
            'meta_description': 'Sustainable bags and eco-friendly fashion accessories. Recycled materials, ethical production, and stylish design.'
        },
        {
            'name': 'Footwear',
            'slug': 'footwear',
            'parent_slug': 'fashion-accessories',
            'description': 'Sustainable and comfortable footwear made from eco-friendly materials',
            'image_url': '/static/images/categories/footwear.jpg',
            'icon_class': 'fas fa-shoe-prints',
            'meta_title': 'Sustainable Footwear | Eco-Friendly Shoes',
            'meta_description': 'Sustainable footwear and eco-friendly shoes. Comfortable, durable, and made from natural and recycled materials.'
        },
        {
            'name': 'Jewelry',
            'slug': 'jewelry',
            'parent_slug': 'fashion-accessories',
            'description': 'Ethically sourced and sustainable jewelry made from recycled metals',
            'image_url': '/static/images/categories/jewelry.jpg',
            'icon_class': 'fas fa-gem',
            'meta_title': 'Sustainable Jewelry | Ethical & Recycled Accessories',
            'meta_description': 'Sustainable jewelry and ethical accessories. Recycled metals, conflict-free gems, and responsible craftsmanship.'
        },
        # Food & Beverages children
        {
            'name': 'Organic Foods',
            'slug': 'organic-foods',
            'parent_slug': 'food-beverages',
            'description': 'Certified organic food products and natural ingredients',
            'image_url': '/static/images/categories/organic-foods.jpg',
            'icon_class': 'fas fa-seedling',
            'meta_title': 'Organic Food Products | Natural & Healthy Ingredients',
            'meta_description': 'Certified organic food products and natural ingredients. Healthy, sustainable nutrition for you and your family.'
        },
        {
            'name': 'Beverages',
            'slug': 'beverages',
            'parent_slug': 'food-beverages',
            'description': 'Natural and organic beverages, teas, and healthy drinks',
            'image_url': '/static/images/categories/beverages.jpg',
            'icon_class': 'fas fa-coffee',
            'meta_title': 'Natural Beverages | Organic Teas & Healthy Drinks',
            'meta_description': 'Natural beverages and organic drinks. Herbal teas, natural juices, and healthy beverage options.'
        },
        {
            'name': 'Snacks',
            'slug': 'snacks',
            'parent_slug': 'food-beverages',
            'description': 'Healthy and sustainable snack options made from natural ingredients',
            'image_url': '/static/images/categories/snacks.jpg',
            'icon_class': 'fas fa-cookie-bite',
            'meta_title': 'Healthy Snacks | Natural & Organic Snack Foods',
            'meta_description': 'Healthy snacks and natural snack foods. Organic ingredients, sustainable packaging, and delicious flavors.'
        },
        {
            'name': 'Supplements',
            'slug': 'supplements',
            'parent_slug': 'food-beverages',
            'description': 'Natural health supplements and vitamins from organic sources',
            'image_url': '/static/images/categories/supplements.jpg',
            'icon_class': 'fas fa-pills',
            'meta_title': 'Natural Health Supplements | Organic Vitamins',
            'meta_description': 'Natural health supplements and organic vitamins. Plant-based nutrition and sustainable wellness products.'
        },
        # Baby & Kids children
        {
            'name': 'Baby Care',
            'slug': 'baby-care',
            'parent_slug': 'baby-kids',
            'description': 'Natural baby care and hygiene products safe for sensitive skin',
            'image_url': '/static/images/categories/baby-care.jpg',
            'icon_class': 'fas fa-baby-carriage',
            'meta_title': 'Natural Baby Care Products | Safe Baby Hygiene',
            'meta_description': 'Natural baby care and safe hygiene products. Chemical-free, gentle, and perfect for sensitive baby skin.'
        },
        {
            'name': 'Toys & Games',
            'slug': 'toys-games',
            'parent_slug': 'baby-kids',
            'description': 'Educational and sustainable toys made from natural, safe materials',
            'image_url': '/static/images/categories/toys-games.jpg',
            'icon_class': 'fas fa-puzzle-piece',
            'meta_title': 'Educational Toys & Sustainable Games | Safe Children\'s Play',
            'meta_description': 'Educational toys and sustainable games. Natural materials, safe design, and developmental benefits for children.'
        },
        {
            'name': 'Kids Clothing',
            'slug': 'kids-clothing',
            'parent_slug': 'baby-kids',
            'description': 'Organic and sustainable kids clothing made from natural fibers',
            'image_url': '/static/images/categories/kids-clothing.jpg',
            'icon_class': 'fas fa-child',
            'meta_title': 'Organic Kids Clothing | Sustainable Children\'s Apparel',
            'meta_description': 'Organic kids clothing and sustainable children\'s apparel. Natural fibers, safe dyes, and comfortable designs.'
        },
        {
            'name': 'Feeding',
            'slug': 'feeding',
            'parent_slug': 'baby-kids',
            'description': 'Safe and sustainable feeding products for babies and toddlers',
            'image_url': '/static/images/categories/feeding.jpg',
            'icon_class': 'fas fa-baby-bottle',
            'meta_title': 'Safe Baby Feeding Products | Sustainable Feeding Solutions',
            'meta_description': 'Safe baby feeding products and sustainable feeding solutions. BPA-free, natural materials, and healthy feeding options.'
        }
    ]

    # Create child categories
    for child_data in child_categories_data:
        parent_category = created_categories[child_data['parent_slug']]

        child_category = Category(
            name=child_data['name'],
            slug=child_data['slug'],
            description=child_data['description'],
            parent_id=parent_category.id,
            level=1,
            sort_order=0,
            image_url=child_data['image_url'],
            icon_class=child_data['icon_class'],
            meta_title=child_data['meta_title'],
            meta_description=child_data['meta_description'],
            is_active=True,
            is_featured=False,
            product_count=0,  # Will be updated later
            created_at=datetime.now() - timedelta(days=random.randint(30, 120)),
            updated_at=datetime.now() - timedelta(days=random.randint(1, 20))
        )
        db.session.add(child_category)
        created_categories[child_data['slug']] = child_category

    db.session.commit()

    print(f"✅ Created {len(created_categories)} categories (including child categories)")
    return created_categories

def get_sustainable_products_data():
    """Get data for 50 sustainable products with ALL fields populated"""
    return [
        # Kitchen & Dining (12 products)
        {
            'name': 'Bamboo Cutting Board Set',
            'category': 'kitchen-dining',
            'brand': 'GreenChef',
            'price': 45.99,
            'description': 'Set of 3 bamboo cutting boards in different sizes. Naturally antimicrobial and knife-friendly. Perfect for sustainable cooking.',
            'material': 'Sustainable Bamboo',
            'sustainability_score': 95,
            'stock_quantity': 150,
            'weight': 1.2,
            'dimensions': '35x25x2 cm (Large), 30x20x2 cm (Medium), 25x15x2 cm (Small)',
            'care_instructions': 'Hand wash with mild soap and warm water. Oil monthly with food-safe mineral oil to maintain.',
            'low_stock_threshold': 20,
            'seller_id': None  # Admin-managed product
        },
        {
            'name': 'Stainless Steel Lunch Box',
            'category': 'kitchen-dining',
            'brand': 'PureLunch',
            'price': 32.50,
            'description': 'Leak-proof stainless steel lunch container with compartments. Perfect for meal prep and zero-waste lunches.',
            'material': '304 Stainless Steel, Silicone Seals',
            'sustainability_score': 90,
            'stock_quantity': 200,
            'weight': 0.8,
            'dimensions': '20x15x6 cm',
            'care_instructions': 'Dishwasher safe. Avoid abrasive cleaners. Remove silicone seals for thorough cleaning.',
            'low_stock_threshold': 25,
            'seller_id': None
        },
        {
            'name': 'Glass Food Storage Jars',
            'category': 'kitchen-dining',
            'brand': 'ClearStore',
            'price': 28.99,
            'description': 'Set of 6 borosilicate glass jars with airtight bamboo lids. Perfect for pantry organization and food storage.',
            'material': 'Borosilicate Glass, Bamboo Lids',
            'sustainability_score': 88,
            'stock_quantity': 120,
            'weight': 2.1,
            'dimensions': '10x10x15 cm each (500ml capacity)',
            'care_instructions': 'Dishwasher safe glass containers. Hand wash bamboo lids with mild soap.',
            'low_stock_threshold': 15,
            'seller_id': None
        },
        {
            'name': 'Silicone Food Wraps',
            'category': 'kitchen-dining',
            'brand': 'WrapFresh',
            'price': 19.99,
            'description': 'Reusable silicone food wraps in assorted sizes. Replace plastic wrap and aluminum foil for sustainable food storage.',
            'material': 'Food-grade Platinum Silicone',
            'sustainability_score': 85,
            'stock_quantity': 180,
            'weight': 0.3,
            'dimensions': 'Small (15x15cm), Medium (20x20cm), Large (25x25cm)',
            'care_instructions': 'Dishwasher safe or hand wash with warm soapy water. Air dry completely before storing.',
            'low_stock_threshold': 30,
            'seller_id': None
        },
        {
            'name': 'Copper Water Bottle',
            'category': 'kitchen-dining',
            'brand': 'PureCopper',
            'price': 42.00,
            'description': 'Handcrafted pure copper water bottle with natural antimicrobial properties. Ayurvedic health benefits.',
            'material': '99.9% Pure Copper',
            'sustainability_score': 92,
            'stock_quantity': 95,
            'weight': 0.4,
            'dimensions': '26x7 cm (750ml capacity)',
            'care_instructions': 'Hand wash only. Clean with lemon and salt weekly. Avoid acidic beverages.',
            'low_stock_threshold': 10,
            'seller_id': None
        },
        {
            'name': 'Wooden Spoon Set',
            'category': 'kitchen-dining',
            'brand': 'ForestCraft',
            'price': 24.50,
            'description': 'Set of 5 handcrafted wooden spoons from sustainably sourced hardwood. Perfect for non-stick cookware.',
            'material': 'FSC Certified Beech Wood',
            'sustainability_score': 93,
            'stock_quantity': 140,
            'weight': 0.2,
            'dimensions': '25-30 cm length, various shapes',
            'care_instructions': 'Hand wash and dry immediately. Oil monthly with food-safe mineral oil.',
            'low_stock_threshold': 20,
            'seller_id': None
        },
        {
            'name': 'Hemp Dish Towels',
            'category': 'kitchen-dining',
            'brand': 'NaturalClean',
            'price': 16.99,
            'description': 'Pack of 4 absorbent hemp dish towels. Naturally antimicrobial and quick-drying for sustainable kitchen cleaning.',
            'material': '100% Organic Hemp',
            'sustainability_score': 89,
            'stock_quantity': 220,
            'weight': 0.3,
            'dimensions': '40x30 cm each',
            'care_instructions': 'Machine washable in cold water. Tumble dry low or air dry. Gets softer with each wash.',
            'low_stock_threshold': 35,
            'seller_id': None
        },
        {
            'name': 'Ceramic Dinnerware Set',
            'category': 'kitchen-dining',
            'brand': 'EarthTable',
            'price': 89.99,
            'description': '16-piece ceramic dinnerware set with natural glaze. Microwave and dishwasher safe for everyday use.',
            'material': 'Lead-free Ceramic, Natural Glaze',
            'sustainability_score': 87,
            'stock_quantity': 75,
            'weight': 8.5,
            'dimensions': '4 dinner plates (27cm), 4 salad plates (20cm), 4 bowls (15cm), 4 mugs (350ml)',
            'care_instructions': 'Dishwasher and microwave safe. Avoid thermal shock by gradual temperature changes.',
            'low_stock_threshold': 8,
            'seller_id': None
        },
        {
            'name': 'Compost Bin',
            'category': 'kitchen-dining',
            'brand': 'GreenCycle',
            'price': 35.00,
            'description': 'Countertop compost bin with charcoal filter. Reduces kitchen waste and odors for sustainable living.',
            'material': 'Stainless Steel, Bamboo Handle, Activated Charcoal Filter',
            'sustainability_score': 94,
            'stock_quantity': 110,
            'weight': 1.1,
            'dimensions': '20x15x20 cm (3L capacity)',
            'care_instructions': 'Hand wash bin with mild soap. Replace charcoal filter every 6 months.',
            'low_stock_threshold': 15,
            'seller_id': None
        },
        {
            'name': 'Beeswax Food Wraps',
            'category': 'kitchen-dining',
            'brand': 'BeeNatural',
            'price': 22.50,
            'description': 'Set of organic cotton wraps infused with beeswax, jojoba oil, and tree resin. Natural food preservation.',
            'material': 'GOTS Organic Cotton, Pure Beeswax, Jojoba Oil, Tree Resin',
            'sustainability_score': 96,
            'stock_quantity': 160,
            'weight': 0.2,
            'dimensions': 'Small (18x20cm), Medium (25x28cm), Large (33x35cm)',
            'care_instructions': 'Rinse with cool water and mild soap. Air dry. Refresh with beeswax bar as needed.',
            'low_stock_threshold': 25,
            'seller_id': None
        },
        {
            'name': 'Cast Iron Skillet',
            'category': 'kitchen-dining',
            'brand': 'IronCraft',
            'price': 65.00,
            'description': 'Pre-seasoned cast iron skillet. Naturally non-stick surface that improves with use. Lifetime durability.',
            'material': 'Cast Iron with Organic Flaxseed Oil Seasoning',
            'sustainability_score': 91,
            'stock_quantity': 85,
            'weight': 2.3,
            'dimensions': '25 cm diameter, 5 cm depth',
            'care_instructions': 'Hand wash with hot water. Dry immediately and apply thin layer of oil. Season regularly.',
            'low_stock_threshold': 10,
            'seller_id': None
        },
        {
            'name': 'Reusable Coffee Filters',
            'category': 'kitchen-dining',
            'brand': 'BrewGreen',
            'price': 18.99,
            'description': 'Set of 2 stainless steel mesh coffee filters. Eliminates paper filter waste for sustainable brewing.',
            'material': '304 Stainless Steel Mesh, BPA-free Plastic Rim',
            'sustainability_score': 88,
            'stock_quantity': 130,
            'weight': 0.15,
            'dimensions': 'Standard pour-over size (fits most coffee makers)',
            'care_instructions': 'Rinse after each use. Deep clean weekly with baking soda solution.',
            'low_stock_threshold': 20,
            'seller_id': None
        },

        # Personal Care - Skincare (10 products)
        {
            'name': 'Organic Face Cleanser',
            'category': 'skincare',
            'brand': 'PureGlow',
            'price': 24.99,
            'description': 'Gentle organic face cleanser with chamomile and aloe vera. Suitable for all skin types, removes makeup naturally.',
            'material': 'Organic Chamomile, Aloe Vera, Coconut Oil, Essential Oils',
            'sustainability_score': 92,
            'stock_quantity': 180,
            'weight': 0.15,
            'dimensions': '15x5 cm bottle (150ml)',
            'care_instructions': 'Store in cool, dry place away from direct sunlight. Use within 12 months of opening.',
            'low_stock_threshold': 25,
            'seller_id': None
        },
        {
            'name': 'Natural Moisturizing Cream',
            'category': 'skincare',
            'brand': 'EcoBeauty',
            'price': 32.50,
            'description': 'Rich moisturizing cream with shea butter and jojoba oil. Deeply hydrates without greasiness.',
            'material': 'Organic Shea Butter, Jojoba Oil, Vitamin E, Natural Preservatives',
            'sustainability_score': 90,
            'stock_quantity': 145,
            'weight': 0.12,
            'dimensions': '8x8x4 cm jar (50ml)',
            'care_instructions': 'Keep lid tightly closed. Avoid direct sunlight. Use clean hands or spatula.',
            'low_stock_threshold': 20,
            'seller_id': None
        },
        {
            'name': 'Vitamin C Serum',
            'category': 'skincare',
            'brand': 'GlowNaturals',
            'price': 45.00,
            'description': 'Potent vitamin C serum with hyaluronic acid. Brightens skin and reduces fine lines naturally.',
            'material': 'L-Ascorbic Acid, Hyaluronic Acid, Vitamin E, Natural Stabilizers',
            'sustainability_score': 88,
            'stock_quantity': 95,
            'weight': 0.08,
            'dimensions': '12x3 cm dropper bottle (30ml)',
            'care_instructions': 'Refrigerate after opening. Use within 6 months. Apply sunscreen during day use.',
            'low_stock_threshold': 12,
            'seller_id': None
        },
        {
            'name': 'Clay Face Mask',
            'category': 'skincare',
            'brand': 'EarthClay',
            'price': 19.99,
            'description': 'Detoxifying clay mask with bentonite and kaolin clay. Purifies pores and tightens skin naturally.',
            'material': 'Bentonite Clay, Kaolin Clay, French Green Clay, Natural Minerals',
            'sustainability_score': 94,
            'stock_quantity': 200,
            'weight': 0.2,
            'dimensions': '10x10x3 cm jar (100g)',
            'care_instructions': 'Keep container dry. Use clean, dry spoon to scoop product. Avoid metal utensils.',
            'low_stock_threshold': 30,
            'seller_id': None
        },
        {
            'name': 'Rose Hip Oil',
            'category': 'skincare',
            'brand': 'PureOils',
            'price': 28.00,
            'description': 'Cold-pressed rose hip oil rich in vitamins A and C. Anti-aging and skin regenerating properties.',
            'material': '100% Cold-pressed Rose Hip Seed Oil',
            'sustainability_score': 96,
            'stock_quantity': 120,
            'weight': 0.06,
            'dimensions': '10x3 cm bottle (30ml)',
            'care_instructions': 'Store in dark, cool place. Use within 12 months. Patch test before first use.',
            'low_stock_threshold': 15,
            'seller_id': None
        },
        {
            'name': 'Exfoliating Sugar Scrub',
            'category': 'skincare',
            'brand': 'SweetSkin',
            'price': 21.50,
            'description': 'Gentle sugar scrub with coconut oil and vanilla. Removes dead skin cells naturally for smooth skin.',
            'material': 'Organic Cane Sugar, Virgin Coconut Oil, Vanilla Extract, Vitamin E',
            'sustainability_score': 89,
            'stock_quantity': 165,
            'weight': 0.25,
            'dimensions': '12x8 cm jar (200g)',
            'care_instructions': 'Keep lid closed tightly. Avoid water contamination. Use dry hands to scoop.',
            'low_stock_threshold': 25,
            'seller_id': None
        },
        {
            'name': 'Aloe Vera Gel',
            'category': 'skincare',
            'brand': 'AloeNature',
            'price': 16.99,
            'description': '99% pure aloe vera gel. Soothes sunburn, cuts, and skin irritation. Multi-purpose healing gel.',
            'material': '99% Pure Aloe Vera Gel, Natural Preservatives',
            'sustainability_score': 95,
            'stock_quantity': 210,
            'weight': 0.18,
            'dimensions': '15x6 cm tube (150ml)',
            'care_instructions': 'Refrigerate for cooling effect. Use within 18 months. External use only.',
            'low_stock_threshold': 35,
            'seller_id': None
        },
        {
            'name': 'Retinol Night Cream',
            'category': 'skincare',
            'brand': 'NightGlow',
            'price': 38.99,
            'description': 'Gentle retinol night cream with peptides. Reduces wrinkles and improves skin texture overnight.',
            'material': 'Plant-derived Retinol, Peptides, Hyaluronic Acid, Natural Moisturizers',
            'sustainability_score': 87,
            'stock_quantity': 105,
            'weight': 0.14,
            'dimensions': '9x9x4 cm jar (50ml)',
            'care_instructions': 'Use only at night. Store away from light and heat. Start with 2-3 times per week.',
            'low_stock_threshold': 15,
            'seller_id': None
        },
        {
            'name': 'Sunscreen SPF 30',
            'category': 'skincare',
            'brand': 'SunSafe',
            'price': 26.50,
            'description': 'Natural mineral sunscreen with zinc oxide. Reef-safe formula protects without harmful chemicals.',
            'material': 'Zinc Oxide, Titanium Dioxide, Coconut Oil, Shea Butter',
            'sustainability_score': 91,
            'stock_quantity': 155,
            'weight': 0.1,
            'dimensions': '12x4 cm tube (75ml)',
            'care_instructions': 'Shake well before use. Reapply every 2 hours. Water-resistant for 80 minutes.',
            'low_stock_threshold': 20,
            'seller_id': None
        },
        {
            'name': 'Eye Cream',
            'category': 'skincare',
            'brand': 'EyeCare',
            'price': 34.99,
            'description': 'Anti-aging eye cream with caffeine and peptides. Reduces puffiness and dark circles naturally.',
            'material': 'Caffeine, Peptides, Hyaluronic Acid, Vitamin K, Natural Oils',
            'sustainability_score': 88,
            'stock_quantity': 125,
            'weight': 0.05,
            'dimensions': '8x3 cm tube (15ml)',
            'care_instructions': 'Apply gently around eye area. Use morning and night. Avoid direct contact with eyes.',
            'low_stock_threshold': 18,
            'seller_id': None
        },

        # Hair Care (6 products)
        {
            'name': 'Sulfate-Free Shampoo',
            'category': 'hair-care',
            'brand': 'PureHair',
            'price': 26.99,
            'description': 'Gentle sulfate-free shampoo with argan oil and keratin. Safe for color-treated hair and sensitive scalps.',
            'material': 'Argan Oil, Hydrolyzed Keratin, Natural Cleansers, Essential Oils',
            'sustainability_score': 91,
            'stock_quantity': 175,
            'weight': 0.35,
            'dimensions': '20x6 cm bottle (300ml)',
            'care_instructions': 'Store upright in cool, dry place. Avoid extreme temperatures. Shake before use.',
            'low_stock_threshold': 25,
            'seller_id': None
        },
        {
            'name': 'Organic Conditioner',
            'category': 'hair-care',
            'brand': 'NaturalLocks',
            'price': 24.50,
            'description': 'Deep conditioning treatment with coconut oil and shea butter. Repairs damaged hair and adds shine.',
            'material': 'Virgin Coconut Oil, Organic Shea Butter, Protein Complex, Natural Extracts',
            'sustainability_score': 93,
            'stock_quantity': 160,
            'weight': 0.32,
            'dimensions': '18x6 cm bottle (250ml)',
            'care_instructions': 'Keep cap closed tightly. Use within 12 months of opening. Apply to mid-lengths and ends.',
            'low_stock_threshold': 22,
            'seller_id': None
        },
        {
            'name': 'Hair Growth Serum',
            'category': 'hair-care',
            'brand': 'GrowStrong',
            'price': 42.00,
            'description': 'Stimulating hair growth serum with biotin and rosemary oil. Promotes thicker, healthier hair growth.',
            'material': 'Biotin, Rosemary Essential Oil, Peptides, Caffeine, Natural Oils',
            'sustainability_score': 89,
            'stock_quantity': 90,
            'weight': 0.1,
            'dimensions': '12x3 cm dropper bottle (50ml)',
            'care_instructions': 'Apply to clean, dry scalp. Massage gently. Use 2-3 times per week for best results.',
            'low_stock_threshold': 12,
            'seller_id': None
        },
        {
            'name': 'Dry Shampoo Powder',
            'category': 'hair-care',
            'brand': 'FreshRoots',
            'price': 18.99,
            'description': 'Natural dry shampoo powder with rice starch and essential oils. Absorbs oil and adds volume.',
            'material': 'Rice Starch, Kaolin Clay, Arrowroot Powder, Essential Oils',
            'sustainability_score': 94,
            'stock_quantity': 140,
            'weight': 0.15,
            'dimensions': '8x8x8 cm container (100g)',
            'care_instructions': 'Keep container dry and sealed. Shake well before use. Apply to roots only.',
            'low_stock_threshold': 20,
            'seller_id': None
        },
        {
            'name': 'Leave-in Hair Treatment',
            'category': 'hair-care',
            'brand': 'SilkStrands',
            'price': 29.99,
            'description': 'Lightweight leave-in treatment with silk proteins and vitamin E. Protects and smooths hair.',
            'material': 'Hydrolyzed Silk Protein, Vitamin E, Argan Oil, UV Filters',
            'sustainability_score': 88,
            'stock_quantity': 125,
            'weight': 0.2,
            'dimensions': '15x5 cm spray bottle (150ml)',
            'care_instructions': 'Spray on damp or dry hair. Do not rinse. Avoid contact with eyes.',
            'low_stock_threshold': 18,
            'seller_id': None
        },
        {
            'name': 'Bamboo Hair Brush',
            'category': 'hair-care',
            'brand': 'EcoBrush',
            'price': 22.50,
            'description': 'Sustainable bamboo hair brush with natural boar bristles. Gentle on hair and scalp, reduces static.',
            'material': 'FSC Certified Bamboo, Natural Boar Bristles',
            'sustainability_score': 96,
            'stock_quantity': 110,
            'weight': 0.12,
            'dimensions': '23x7x3 cm',
            'care_instructions': 'Clean bristles weekly with mild shampoo. Keep dry. Replace every 12-18 months.',
            'low_stock_threshold': 15,
            'seller_id': None
        },

        # Fashion & Accessories - Clothing (8 products)
        {
            'name': 'Organic Cotton T-Shirt',
            'category': 'clothing',
            'brand': 'EcoWear',
            'price': 29.99,
            'description': 'Soft organic cotton t-shirt in classic fit. GOTS certified and ethically made in fair trade facilities.',
            'material': 'GOTS Certified Organic Cotton (100%)',
            'sustainability_score': 92,
            'stock_quantity': 200,
            'weight': 0.18,
            'dimensions': 'Available in XS-XXL (size chart available)',
            'care_instructions': 'Machine wash cold with like colors. Tumble dry low. Iron on medium heat if needed.',
            'low_stock_threshold': 30,
            'seller_id': None
        },
        {
            'name': 'Hemp Denim Jeans',
            'category': 'clothing',
            'brand': 'GreenDenim',
            'price': 89.99,
            'description': 'Durable hemp-cotton blend jeans. Sustainable alternative to traditional denim with superior comfort.',
            'material': '55% Hemp Fiber, 45% Organic Cotton',
            'sustainability_score': 88,
            'stock_quantity': 85,
            'weight': 0.6,
            'dimensions': 'Available in sizes 28-42 waist, various lengths',
            'care_instructions': 'Wash inside out in cold water. Air dry recommended. Iron on medium heat.',
            'low_stock_threshold': 12,
            'seller_id': None
        },
        {
            'name': 'Bamboo Fiber Socks',
            'category': 'clothing',
            'brand': 'ComfortFeet',
            'price': 16.99,
            'description': 'Pack of 3 bamboo fiber socks. Naturally antibacterial, moisture-wicking, and incredibly soft.',
            'material': '70% Bamboo Fiber, 25% Organic Cotton, 5% Elastane',
            'sustainability_score': 90,
            'stock_quantity': 250,
            'weight': 0.08,
            'dimensions': 'Available in S, M, L, XL sizes',
            'care_instructions': 'Machine wash warm. Do not bleach. Tumble dry low or air dry.',
            'low_stock_threshold': 40,
            'seller_id': None
        },
        {
            'name': 'Recycled Polyester Jacket',
            'category': 'clothing',
            'brand': 'ReWear',
            'price': 125.00,
            'description': 'Lightweight jacket made from recycled plastic bottles. Water-resistant and windproof for outdoor activities.',
            'material': '100% Recycled Polyester (made from 25 plastic bottles)',
            'sustainability_score': 85,
            'stock_quantity': 60,
            'weight': 0.4,
            'dimensions': 'Available in XS-XXL (athletic fit)',
            'care_instructions': 'Machine wash cold on gentle cycle. Hang dry. Do not iron or dry clean.',
            'low_stock_threshold': 8,
            'seller_id': None
        },
        {
            'name': 'Linen Button-Up Shirt',
            'category': 'clothing',
            'brand': 'LinenLux',
            'price': 68.00,
            'description': 'Classic linen shirt in natural color. Breathable and perfect for warm weather, gets softer with wear.',
            'material': '100% European Flax Linen',
            'sustainability_score': 91,
            'stock_quantity': 95,
            'weight': 0.25,
            'dimensions': 'Available in XS-XXL (relaxed fit)',
            'care_instructions': 'Machine wash cold. Iron while damp for best results. Dry cleaning optional.',
            'low_stock_threshold': 12,
            'seller_id': None
        },
        {
            'name': 'Merino Wool Sweater',
            'category': 'clothing',
            'brand': 'WoolCraft',
            'price': 98.50,
            'description': 'Ethically sourced merino wool sweater. Temperature regulating, odor resistant, and incredibly soft.',
            'material': 'Ethically Sourced Merino Wool (100%)',
            'sustainability_score': 87,
            'stock_quantity': 70,
            'weight': 0.35,
            'dimensions': 'Available in XS-XXL (classic fit)',
            'care_instructions': 'Hand wash in cool water with wool detergent. Lay flat to dry. Store folded.',
            'low_stock_threshold': 10,
            'seller_id': None
        },
        {
            'name': 'Tencel Dress',
            'category': 'clothing',
            'brand': 'FlowFashion',
            'price': 79.99,
            'description': 'Elegant dress made from Tencel lyocell. Silky smooth, naturally breathable, and drapes beautifully.',
            'material': '100% Tencel Lyocell (sustainably sourced)',
            'sustainability_score': 93,
            'stock_quantity': 80,
            'weight': 0.22,
            'dimensions': 'Available in XS-XXL (A-line fit)',
            'care_instructions': 'Machine wash cold on gentle cycle. Hang dry. Iron on low heat if needed.',
            'low_stock_threshold': 10,
            'seller_id': None
        },
        {
            'name': 'Organic Cotton Underwear Set',
            'category': 'clothing',
            'brand': 'PureComfort',
            'price': 34.99,
            'description': 'Set of 3 organic cotton underwear. Soft, breathable, and chemical-free for sensitive skin.',
            'material': 'GOTS Certified Organic Cotton (95%), Elastane (5%)',
            'sustainability_score': 94,
            'stock_quantity': 180,
            'weight': 0.12,
            'dimensions': 'Available in XS-XXL (various styles)',
            'care_instructions': 'Machine wash warm with like colors. Tumble dry low. Avoid fabric softeners.',
            'low_stock_threshold': 25,
            'seller_id': None
        },

        # Food & Beverages - Organic Foods (8 products)
        {
            'name': 'Organic Quinoa',
            'category': 'organic-foods',
            'brand': 'PureGrains',
            'price': 12.99,
            'description': 'Premium organic quinoa from Bolivia. Complete protein and gluten-free superfood for healthy meals.',
            'material': 'Certified Organic Quinoa Seeds',
            'sustainability_score': 95,
            'stock_quantity': 300,
            'weight': 0.5,
            'dimensions': '15x10x5 cm package (500g)',
            'care_instructions': 'Store in cool, dry place in airtight container. Use within 2 years of purchase.',
            'low_stock_threshold': 50,
            'seller_id': None
        },
        {
            'name': 'Raw Honey',
            'category': 'organic-foods',
            'brand': 'BeeKeepers',
            'price': 18.50,
            'description': 'Unprocessed raw honey from wildflower meadows. Rich in enzymes, antioxidants, and natural minerals.',
            'material': '100% Raw Wildflower Honey (unfiltered, unpasteurized)',
            'sustainability_score': 92,
            'stock_quantity': 150,
            'weight': 0.45,
            'dimensions': '12x8 cm glass jar (400g)',
            'care_instructions': 'Store at room temperature. Crystallization is natural - warm gently to liquefy.',
            'low_stock_threshold': 20,
            'seller_id': None
        },
        {
            'name': 'Organic Coconut Oil',
            'category': 'organic-foods',
            'brand': 'TropicalPure',
            'price': 15.99,
            'description': 'Cold-pressed virgin coconut oil. Perfect for cooking, baking, and natural skincare applications.',
            'material': 'Cold-pressed Virgin Coconut Oil (unrefined)',
            'sustainability_score': 89,
            'stock_quantity': 200,
            'weight': 0.5,
            'dimensions': '12x8 cm glass jar (500ml)',
            'care_instructions': 'Store in cool place. Solid below 24°C, liquid above. Both forms are normal.',
            'low_stock_threshold': 30,
            'seller_id': None
        },
        {
            'name': 'Himalayan Pink Salt',
            'category': 'organic-foods',
            'brand': 'MountainPure',
            'price': 9.99,
            'description': 'Pure Himalayan pink salt crystals. Rich in 84 trace minerals and perfect for seasoning.',
            'material': '100% Pure Himalayan Pink Salt (hand-mined)',
            'sustainability_score': 88,
            'stock_quantity': 250,
            'weight': 0.5,
            'dimensions': '15x10x5 cm package (500g)',
            'care_instructions': 'Store in dry place away from moisture. Keep container sealed to prevent clumping.',
            'low_stock_threshold': 40,
            'seller_id': None
        },
        {
            'name': 'Organic Chia Seeds',
            'category': 'organic-foods',
            'brand': 'SuperSeeds',
            'price': 14.50,
            'description': 'Premium organic chia seeds. High in omega-3 fatty acids, fiber, and complete protein.',
            'material': 'Certified Organic Chia Seeds (Salvia hispanica)',
            'sustainability_score': 94,
            'stock_quantity': 180,
            'weight': 0.45,
            'dimensions': '12x8x8 cm container (400g)',
            'care_instructions': 'Store in airtight container in cool, dry place. Use within 4 years of harvest.',
            'low_stock_threshold': 25,
            'seller_id': None
        },
        {
            'name': 'Organic Almond Butter',
            'category': 'organic-foods',
            'brand': 'NutCraft',
            'price': 22.99,
            'description': 'Smooth organic almond butter made from California almonds. No added sugar, oil, or preservatives.',
            'material': '100% Organic Roasted Almonds',
            'sustainability_score': 87,
            'stock_quantity': 120,
            'weight': 0.35,
            'dimensions': '10x8 cm glass jar (350g)',
            'care_instructions': 'Stir before use as oil separation is natural. Refrigerate after opening.',
            'low_stock_threshold': 18,
            'seller_id': None
        },
        {
            'name': 'Organic Green Tea',
            'category': 'beverages',
            'brand': 'LeafPure',
            'price': 16.99,
            'description': 'Premium organic green tea from Japanese gardens. Rich in antioxidants and natural caffeine.',
            'material': 'Organic Green Tea Leaves (Camellia sinensis)',
            'sustainability_score': 93,
            'stock_quantity': 160,
            'weight': 0.1,
            'dimensions': '12x8x8 cm tin (100g loose leaf)',
            'care_instructions': 'Store in cool, dry place away from light and strong odors. Use within 2 years.',
            'low_stock_threshold': 22,
            'seller_id': None
        },
        {
            'name': 'Organic Dark Chocolate',
            'category': 'organic-foods',
            'brand': 'CocoaPure',
            'price': 8.99,
            'description': '85% organic dark chocolate bar. Fair trade and ethically sourced cacao from Ecuador.',
            'material': 'Organic Cacao, Organic Cane Sugar (Fair Trade Certified)',
            'sustainability_score': 91,
            'stock_quantity': 220,
            'weight': 0.1,
            'dimensions': '15x8x1 cm bar (100g)',
            'care_instructions': 'Store in cool, dry place below 18°C. Avoid direct sunlight and strong odors.',
            'low_stock_threshold': 35,
            'seller_id': None
        },

        # Baby & Kids - Baby Care (6 products)
        {
            'name': 'Organic Baby Lotion',
            'category': 'baby-care',
            'brand': 'BabyPure',
            'price': 19.99,
            'description': 'Gentle organic baby lotion with chamomile and calendula. Hypoallergenic and pediatrician tested.',
            'material': 'Organic Chamomile, Calendula, Coconut Oil, Natural Moisturizers',
            'sustainability_score': 95,
            'stock_quantity': 140,
            'weight': 0.2,
            'dimensions': '15x6 cm bottle (200ml)',
            'care_instructions': 'For external use only. Patch test before first use. Store below 25°C.',
            'low_stock_threshold': 20,
            'seller_id': None
        },
        {
            'name': 'Bamboo Baby Bottles',
            'category': 'feeding',
            'brand': 'EcoBaby',
            'price': 32.50,
            'description': 'Set of 2 bamboo baby bottles with silicone nipples. BPA-free and naturally antimicrobial.',
            'material': 'Bamboo Fiber Composite, Food-grade Silicone Nipples',
            'sustainability_score': 92,
            'stock_quantity': 95,
            'weight': 0.3,
            'dimensions': '18x6 cm each (250ml capacity)',
            'care_instructions': 'Sterilize before first use. Hand wash recommended. Replace nipples every 3 months.',
            'low_stock_threshold': 12,
            'seller_id': None
        },
        {
            'name': 'Organic Cotton Baby Clothes',
            'category': 'kids-clothing',
            'brand': 'TinyTogs',
            'price': 45.00,
            'description': 'Set of 5 organic cotton baby onesies. Soft, breathable, and chemical-free for sensitive skin.',
            'material': 'GOTS Certified Organic Cotton (100%)',
            'sustainability_score': 94,
            'stock_quantity': 120,
            'weight': 0.25,
            'dimensions': 'Available in newborn to 24 months',
            'care_instructions': 'Machine wash warm with baby detergent. Tumble dry low. Iron on low heat.',
            'low_stock_threshold': 15,
            'seller_id': None
        },
        {
            'name': 'Natural Baby Shampoo',
            'category': 'baby-care',
            'brand': 'GentleCare',
            'price': 16.50,
            'description': 'Mild baby shampoo with organic calendula. No sulfates, parabens, or artificial fragrances.',
            'material': 'Organic Calendula, Natural Cleansers, Chamomile Extract',
            'sustainability_score': 93,
            'stock_quantity': 170,
            'weight': 0.25,
            'dimensions': '18x6 cm bottle (250ml)',
            'care_instructions': 'Avoid contact with eyes. Rinse thoroughly. For external use only.',
            'low_stock_threshold': 25,
            'seller_id': None
        },
        {
            'name': 'Wooden Baby Teether',
            'category': 'toys-games',
            'brand': 'SafePlay',
            'price': 12.99,
            'description': 'Natural wooden teething ring. Smooth finish, safe for babies to chew, and helps soothe gums.',
            'material': 'Untreated Beech Wood (FSC Certified)',
            'sustainability_score': 96,
            'stock_quantity': 200,
            'weight': 0.05,
            'dimensions': '8x8x2 cm ring',
            'care_instructions': 'Wipe clean with damp cloth. Do not submerge in water. Check regularly for damage.',
            'low_stock_threshold': 30,
            'seller_id': None
        },
        {
            'name': 'Cloth Diaper Set',
            'category': 'baby-care',
            'brand': 'EcoDiapers',
            'price': 89.99,
            'description': 'Set of 6 reusable cloth diapers with bamboo inserts. Adjustable sizing and leak-proof design.',
            'material': 'Organic Cotton Outer, Bamboo Fiber Inserts, PUL Waterproof Layer',
            'sustainability_score': 97,
            'stock_quantity': 75,
            'weight': 0.8,
            'dimensions': 'One size fits 8-35 lbs (adjustable snaps)',
            'care_instructions': 'Pre-wash before first use. Machine wash hot. Line dry recommended.',
            'low_stock_threshold': 8,
            'seller_id': None
        }
    ]

def create_products(categories):
    """Create 50 sustainable products with ALL fields populated"""
    print("🛍️ Creating 50 sustainable products...")

    products_data = get_sustainable_products_data()
    created_products = []

    for product_data in products_data:
        # Find the category
        category_slug = product_data['category']
        category = categories.get(category_slug)

        if not category:
            print(f"⚠️ Category '{category_slug}' not found for product '{product_data['name']}'")
            continue

        # Generate unique SKU
        sku = f"ECO-{product_data['name'][:3].upper().replace(' ', '')}-{random.randint(1000, 9999)}"

        # Create product with ALL fields populated
        product = Product(
            name=product_data['name'],
            price=product_data['price'],
            image=f"/static/images/products/{product_data['name'].lower().replace(' ', '_').replace('&', 'and')}_main.jpg",
            sustainability_score=product_data['sustainability_score'],
            stock_quantity=product_data['stock_quantity'],
            seller_id=product_data['seller_id'],  # None for admin-managed products
            description=product_data['description'],
            category=category.name,
            brand=product_data['brand'],
            sku=sku,
            weight=product_data['weight'],
            dimensions=product_data['dimensions'],
            material=product_data['material'],
            care_instructions=product_data['care_instructions'],
            average_rating=round(random.uniform(4.0, 5.0), 1),
            total_reviews=random.randint(5, 50),
            low_stock_threshold=product_data['low_stock_threshold'],
            created_at=datetime.now() - timedelta(days=random.randint(1, 180)),
            updated_at=datetime.now() - timedelta(days=random.randint(0, 30))
        )

        db.session.add(product)
        created_products.append(product)

    db.session.commit()
    print(f"✅ Created {len(created_products)} products")
    return created_products

def create_product_images(products):
    """Create multiple images for each product with ALL fields populated"""
    print("📸 Creating product images...")

    created_images = []

    for product in products:
        # Create 3-5 images per product
        num_images = random.randint(3, 5)

        for i in range(num_images):
            # Generate image URL based on product name and image number
            clean_name = product.name.lower().replace(' ', '_').replace('&', 'and')
            image_url = f"/static/images/products/{clean_name}_{i+1}.jpg"

            # Generate descriptive alt text
            if i == 0:
                alt_text = f"{product.name} - Main product image"
            elif i == 1:
                alt_text = f"{product.name} - Detail view"
            elif i == 2:
                alt_text = f"{product.name} - In use"
            elif i == 3:
                alt_text = f"{product.name} - Packaging"
            else:
                alt_text = f"{product.name} - Additional view {i-3}"

            product_image = ProductImage(
                product_id=product.id,
                image_url=image_url,
                alt_text=alt_text,
                is_primary=(i == 0),  # First image is primary
                display_order=i + 1,
                created_at=datetime.now() - timedelta(days=random.randint(0, 30))
            )

            db.session.add(product_image)
            created_images.append(product_image)

    db.session.commit()
    print(f"✅ Created {len(created_images)} product images")
    return created_images

def create_users():
    """Create sample users with ALL fields populated"""
    print("👥 Creating sample users...")

    users_data = [
        {
            'username': 'eco_sarah_green',
            'email': '<EMAIL>',
            'password': 'user123',
            'first_name': 'Sarah',
            'last_name': 'Green',
            'phone': '******-0101',
            'date_of_birth': date(1990, 5, 15),
            'bio': 'Passionate about sustainable living and eco-friendly products. Zero waste lifestyle advocate.',
            'newsletter_subscribed': True,
            'email_notifications': True,
            'sms_notifications': False,
            'preferred_language': 'en',
            'preferred_currency': 'USD',
            'address': '123 Green Street, Eco City, CA 90210'
        },
        {
            'username': 'sustainable_mike',
            'email': '<EMAIL>',
            'password': 'user123',
            'first_name': 'Mike',
            'last_name': 'Earth',
            'phone': '******-0102',
            'date_of_birth': date(1985, 8, 22),
            'bio': 'Environmental advocate and zero-waste lifestyle enthusiast. Love supporting ethical brands.',
            'newsletter_subscribed': True,
            'email_notifications': True,
            'sms_notifications': True,
            'preferred_language': 'en',
            'preferred_currency': 'USD',
            'address': '456 Nature Ave, Green Valley, OR 97001'
        },
        {
            'username': 'green_emma_nature',
            'email': '<EMAIL>',
            'password': 'user123',
            'first_name': 'Emma',
            'last_name': 'Nature',
            'phone': '******-0103',
            'date_of_birth': date(1992, 12, 3),
            'bio': 'Love natural products and supporting ethical brands. Organic food enthusiast.',
            'newsletter_subscribed': False,
            'email_notifications': False,
            'sms_notifications': False,
            'preferred_language': 'en',
            'preferred_currency': 'USD',
            'address': '789 Organic Lane, Natural City, WA 98001'
        },
        {
            'username': 'eco_warrior_alex',
            'email': '<EMAIL>',
            'password': 'user123',
            'first_name': 'Alex',
            'last_name': 'Forest',
            'phone': '******-0104',
            'date_of_birth': date(1988, 3, 10),
            'bio': 'Climate activist and sustainable product reviewer. Fighting for a greener future.',
            'newsletter_subscribed': True,
            'email_notifications': True,
            'sms_notifications': True,
            'preferred_language': 'en',
            'preferred_currency': 'USD',
            'address': '321 Forest Road, Eco Town, TX 75001'
        },
        {
            'username': 'natural_lisa_pure',
            'email': '<EMAIL>',
            'password': 'user123',
            'first_name': 'Lisa',
            'last_name': 'Pure',
            'phone': '******-0105',
            'date_of_birth': date(1995, 7, 18),
            'bio': 'Organic food lover and natural beauty enthusiast. Minimalist lifestyle advocate.',
            'newsletter_subscribed': True,
            'email_notifications': True,
            'sms_notifications': False,
            'preferred_language': 'en',
            'preferred_currency': 'USD',
            'address': '654 Pure Street, Clean City, FL 33101'
        }
    ]

    created_users = []

    for user_data in users_data:
        # Check if user already exists
        existing_user = User.query.filter_by(email=user_data['email']).first()
        if existing_user:
            created_users.append(existing_user)
            continue

        user = User(
            username=user_data['username'],
            email=user_data['email'],
            password=generate_password_hash(user_data['password']),
            first_name=user_data['first_name'],
            last_name=user_data['last_name'],
            phone=user_data['phone'],
            date_of_birth=user_data['date_of_birth'],
            profile_picture=f"/static/images/users/{user_data['username']}.jpg",
            bio=user_data['bio'],
            newsletter_subscribed=user_data['newsletter_subscribed'],
            email_notifications=user_data['email_notifications'],
            sms_notifications=user_data['sms_notifications'],
            preferred_language=user_data['preferred_language'],
            preferred_currency=user_data['preferred_currency'],
            created_at=datetime.now() - timedelta(days=random.randint(30, 365)),
            updated_at=datetime.now() - timedelta(days=random.randint(1, 30)),
            last_login=datetime.now() - timedelta(days=random.randint(0, 7)),
            is_active=True,
            address=user_data['address']
        )

        db.session.add(user)
        created_users.append(user)

    db.session.commit()
    print(f"✅ Created {len(created_users)} sample users")
    return created_users

def create_orders(users, products):
    """Create sample orders with ALL fields populated"""
    print("🛒 Creating sample orders...")

    created_orders = []
    order_items = []

    for i, user in enumerate(users):
        # Create 1-3 orders per user
        num_orders = random.randint(1, 3)

        for j in range(num_orders):
            order_number = f"ORD-{datetime.now().year}-{random.randint(100000, 999999)}"

            # Select 1-4 random products for this order
            selected_products = random.sample(products, random.randint(1, 4))

            subtotal = 0
            order_items_for_order = []

            for product in selected_products:
                quantity = random.randint(1, 3)
                unit_price = product.price
                total_price = unit_price * quantity
                subtotal += total_price

                order_item_data = {
                    'product_id': product.id,
                    'quantity': quantity,
                    'unit_price': unit_price,
                    'total_price': total_price,
                    'product_name': product.name,
                    'product_image': product.image
                }
                order_items_for_order.append(order_item_data)

            # Calculate totals
            tax_amount = round(subtotal * 0.08, 2)  # 8% tax
            shipping_amount = 0 if subtotal > 50 else 5.99  # Free shipping over $50
            discount_amount = round(random.uniform(0, subtotal * 0.1), 2) if random.choice([True, False]) else 0
            total_amount = round(subtotal + tax_amount + shipping_amount - discount_amount, 2)

            # Generate realistic addresses
            addresses = [
                {'name': f"{user.first_name} {user.last_name}", 'address': '123 Green Street', 'city': 'San Francisco', 'state': 'CA', 'zip_code': '94102', 'country': 'USA'},
                {'name': f"{user.first_name} {user.last_name}", 'address': '456 Eco Avenue', 'city': 'Portland', 'state': 'OR', 'zip_code': '97201', 'country': 'USA'},
                {'name': f"{user.first_name} {user.last_name}", 'address': '789 Nature Lane', 'city': 'Seattle', 'state': 'WA', 'zip_code': '98101', 'country': 'USA'},
                {'name': f"{user.first_name} {user.last_name}", 'address': '321 Sustainable Blvd', 'city': 'Austin', 'state': 'TX', 'zip_code': '73301', 'country': 'USA'}
            ]
            shipping_address = random.choice(addresses)

            # Create order with ALL fields populated
            order_date = datetime.now() - timedelta(days=random.randint(1, 90))
            status_options = ['pending', 'confirmed', 'shipped', 'delivered']
            status = random.choice(status_options)

            order = Order(
                user_id=user.id,
                order_number=order_number,
                seller_id=None,  # Admin orders
                is_guest_order=False,
                guest_email=None,
                guest_phone=None,
                guest_session_id=None,
                status=status,
                subtotal=subtotal,
                tax_amount=tax_amount,
                shipping_amount=shipping_amount,
                discount_amount=discount_amount,
                total_amount=total_amount,
                shipping_address=shipping_address,
                billing_address=shipping_address,  # Same as shipping for simplicity
                payment_method=random.choice(['credit_card', 'debit_card', 'paypal', 'apple_pay']),
                payment_status='paid' if status != 'pending' else 'pending',
                payment_reference=f"PAY-{random.randint(100000, 999999)}",
                tracking_number=f"TRK-{random.randint(1000000000, 9999999999)}" if status in ['shipped', 'delivered'] else None,
                estimated_delivery=(order_date + timedelta(days=random.randint(3, 7))).date() if status in ['confirmed', 'shipped'] else None,
                created_at=order_date,
                updated_at=order_date + timedelta(hours=random.randint(1, 48)),
                shipped_at=order_date + timedelta(days=random.randint(1, 3)) if status in ['shipped', 'delivered'] else None,
                delivered_at=order_date + timedelta(days=random.randint(3, 7)) if status == 'delivered' else None,
                order_notes=random.choice([None, 'Please leave at door', 'Ring doorbell', 'Handle with care', 'Gift wrapping requested'])
            )

            db.session.add(order)
            created_orders.append(order)

            # Store order items to create after order is committed
            for item_data in order_items_for_order:
                item_data['order'] = order
                order_items.append(item_data)

    db.session.commit()

    # Create order items with ALL fields populated
    for item_data in order_items:
        order_item = OrderItem(
            order_id=item_data['order'].id,
            product_id=item_data['product_id'],
            quantity=item_data['quantity'],
            unit_price=item_data['unit_price'],
            total_price=item_data['total_price'],
            product_name=item_data['product_name'],
            product_image=item_data['product_image'],
            created_at=item_data['order'].created_at,
            updated_at=item_data['order'].updated_at
        )
        db.session.add(order_item)

    db.session.commit()
    print(f"✅ Created {len(created_orders)} orders with {len(order_items)} order items")
    return created_orders

def create_product_reviews(users, products):
    """Create sample product reviews with ALL fields populated"""
    print("⭐ Creating product reviews...")

    created_reviews = []
    review_titles = [
        "Amazing quality and sustainable!",
        "Love this eco-friendly product",
        "Great value for sustainable living",
        "Highly recommend for green lifestyle",
        "Perfect for environmentally conscious buyers",
        "Excellent eco-friendly choice",
        "Outstanding sustainable quality",
        "Will definitely buy again",
        "Exceeded my green expectations",
        "Fantastic sustainable product",
        "Best eco-friendly purchase ever",
        "Sustainable and stylish",
        "Green living made easy",
        "Environmentally responsible choice",
        "Sustainable quality at its best"
    ]

    review_comments = [
        "This product exceeded my expectations. The quality is outstanding and I love that it's made from sustainable materials. Will definitely recommend to friends who care about the environment.",
        "Love the eco-friendly materials and the quality is exceptional. It's exactly what I was looking for to reduce my environmental footprint.",
        "Perfect addition to my sustainable lifestyle. The product works great and I feel good about supporting environmentally responsible companies.",
        "Great value for the price and the environmental benefits are a huge plus. The packaging was also minimal and recyclable.",
        "Excellent quality and fast shipping. Very happy with this purchase and the company's commitment to sustainability.",
        "The materials feel premium and I love supporting brands that prioritize environmental responsibility. This product delivers on both quality and values.",
        "This product works exactly as described and the sustainable materials make me feel good about my purchase. Highly recommend!",
        "Beautiful design and great functionality. Perfect for anyone looking to make more environmentally conscious choices.",
        "High quality product that aligns perfectly with my environmental values. The craftsmanship is excellent and it's built to last.",
        "Impressed with both the product quality and the sustainable packaging. This company really walks the walk when it comes to environmental responsibility."
    ]

    # Create reviews for random products (about 40% of products get reviews)
    num_reviews = int(len(products) * 0.4 * len(users) * 0.3)  # Realistic review ratio

    for _ in range(num_reviews):
        user = random.choice(users)
        product = random.choice(products)

        # Check if this user already reviewed this product
        existing_review = ProductReview.query.filter_by(
            user_id=user.id,
            product_id=product.id
        ).first()

        if existing_review:
            continue

        review_date = datetime.now() - timedelta(days=random.randint(1, 180))

        review = ProductReview(
            product_id=product.id,
            user_id=user.id,
            rating=random.choices([3, 4, 5], weights=[10, 30, 60])[0],  # Weighted towards higher ratings for sustainable products
            title=random.choice(review_titles),
            comment=random.choice(review_comments),
            verified_purchase=random.choice([True, False]),
            helpful_count=random.randint(0, 25),
            created_at=review_date,
            updated_at=review_date + timedelta(hours=random.randint(0, 24))
        )

        db.session.add(review)
        created_reviews.append(review)

    db.session.commit()
    print(f"✅ Created {len(created_reviews)} product reviews")
    return created_reviews

def create_banners(admin_user):
    """Create promotional banners with ALL fields populated"""
    print("🎨 Creating promotional banners...")

    banners_data = [
        {
            'title': 'Sustainable Living Starts Here',
            'subtitle': 'Discover eco-friendly products for a greener lifestyle. Shop conscious, live sustainably.',
            'image_url': '/static/images/banners/sustainable_living_hero.jpg',
            'link_url': '/categories/home-living',
            'link_text': 'Shop Sustainable Products',
            'opens_new_tab': False,
            'position': 'home_hero',
            'display_order': 1,
            'is_active': True,
            'start_date': datetime.now() - timedelta(days=30),
            'end_date': datetime.now() + timedelta(days=335)
        },
        {
            'title': '50% Off Organic Skincare',
            'subtitle': 'Natural beauty products for healthy, glowing skin. Limited time offer on premium organic skincare.',
            'image_url': '/static/images/banners/organic_skincare_sale.jpg',
            'link_url': '/categories/skincare',
            'link_text': 'Shop Skincare Sale',
            'opens_new_tab': False,
            'position': 'home_secondary',
            'display_order': 2,
            'is_active': True,
            'start_date': datetime.now() - timedelta(days=7),
            'end_date': datetime.now() + timedelta(days=23)
        },
        {
            'title': 'New Bamboo Collection',
            'subtitle': 'Sustainable bamboo products for your home. Naturally antimicrobial and eco-friendly.',
            'image_url': '/static/images/banners/bamboo_collection.jpg',
            'link_url': '/search?q=bamboo',
            'link_text': 'Explore Bamboo Products',
            'opens_new_tab': False,
            'position': 'category_top',
            'display_order': 3,
            'is_active': True,
            'start_date': datetime.now() - timedelta(days=14),
            'end_date': datetime.now() + timedelta(days=76)
        },
        {
            'title': 'Zero Waste Kitchen Essentials',
            'subtitle': 'Transform your kitchen with sustainable alternatives. Reduce waste, increase sustainability.',
            'image_url': '/static/images/banners/zero_waste_kitchen.jpg',
            'link_url': '/categories/kitchen-dining',
            'link_text': 'Shop Kitchen Essentials',
            'opens_new_tab': False,
            'position': 'category_sidebar',
            'display_order': 4,
            'is_active': True,
            'start_date': datetime.now() - timedelta(days=5),
            'end_date': datetime.now() + timedelta(days=55)
        },
        {
            'title': 'Organic Baby Care',
            'subtitle': 'Safe, natural products for your little ones. Chemical-free and gentle on sensitive skin.',
            'image_url': '/static/images/banners/organic_baby_care.jpg',
            'link_url': '/categories/baby-care',
            'link_text': 'Shop Baby Care',
            'opens_new_tab': False,
            'position': 'home_grid',
            'display_order': 5,
            'is_active': True,
            'start_date': datetime.now() - timedelta(days=21),
            'end_date': datetime.now() + timedelta(days=69)
        }
    ]

    created_banners = []

    for banner_data in banners_data:
        banner = Banner(
            title=banner_data['title'],
            subtitle=banner_data['subtitle'],
            image_url=banner_data['image_url'],
            link_url=banner_data['link_url'],
            link_text=banner_data['link_text'],
            opens_new_tab=banner_data['opens_new_tab'],
            position=banner_data['position'],
            display_order=banner_data['display_order'],
            is_active=banner_data['is_active'],
            start_date=banner_data['start_date'],
            end_date=banner_data['end_date'],
            created_at=datetime.now() - timedelta(days=random.randint(1, 60)),
            updated_at=datetime.now() - timedelta(days=random.randint(0, 10)),
            created_by=admin_user.id
        )

        db.session.add(banner)
        created_banners.append(banner)

    db.session.commit()
    print(f"✅ Created {len(created_banners)} promotional banners")
    return created_banners

def update_category_product_counts(categories, products):
    """Update product counts for each category"""
    print("📊 Updating category product counts...")

    for category in categories.values():
        # Count products in this category
        product_count = sum(1 for product in products if product.category == category.name)
        category.product_count = product_count

    db.session.commit()
    print("✅ Updated category product counts")

def main():
    """Main seeding function - seeds ALL tables with complete data"""
    print("🌱 Starting comprehensive database seeding for Allora Sustainable Marketplace")
    print("=" * 80)
    print("This script will populate ALL columns of ALL main tables with realistic data")
    print("=" * 80)

    with app.app_context():
        try:
            # Create admin users first (with all fields)
            admin_users = create_admin_users()

            # Create categories (with all fields including images, meta data)
            categories = create_categories()

            # Create 50 sustainable products (with all fields)
            products = create_products(categories)

            # Create product images (multiple per product)
            product_images = create_product_images(products)

            # Create sample users (with complete profiles)
            users = create_users()

            # Create sample orders (with all financial and tracking data)
            orders = create_orders(users, products)

            # Create product reviews (with ratings and detailed comments)
            reviews = create_product_reviews(users, products)

            # Create promotional banners (with scheduling and display settings)
            banners = create_banners(admin_users[0])

            # Update category product counts
            update_category_product_counts(categories, products)

            print("=" * 80)
            print("🎉 COMPREHENSIVE DATABASE SEEDING COMPLETED SUCCESSFULLY!")
            print("=" * 80)
            print(f"📊 COMPLETE SUMMARY:")
            print(f"   • Admin Users: {len(admin_users)} (with all permissions and metadata)")
            print(f"   • Categories: {len(categories)} (with images, SEO data, hierarchy)")
            print(f"   • Products: {len(products)} (50 sustainable products with ALL fields)")
            print(f"   • Product Images: {len(product_images)} (multiple images per product)")
            print(f"   • Users: {len(users)} (with complete profiles and preferences)")
            print(f"   • Orders: {len(orders)} (with financial data, addresses, tracking)")
            print(f"   • Order Items: {sum(len(order.order_items) for order in orders)} (with product snapshots)")
            print(f"   • Reviews: {len(reviews)} (with ratings, comments, verification)")
            print(f"   • Banners: {len(banners)} (with scheduling and display settings)")
            print("=" * 80)
            print("✨ ALL TABLES SEEDED WITH COMPLETE, REALISTIC DATA!")
            print("🌱 Ready for sustainable ecommerce operations!")
            print("=" * 80)

        except Exception as e:
            print(f"❌ Error during seeding: {e}")
            import traceback
            traceback.print_exc()
            db.session.rollback()
            raise

if __name__ == "__main__":
    main()
