#!/usr/bin/env python3
"""
Database Seeding Script for Allora Sustainable Marketplace
===========================================================

This script seeds the database with:
- 50 sustainable products with realistic names
- Hierarchical category structure
- Product images for each product
- Promotional banners
- Sample data for testing

Usage:
    python seed_database.py

Author: Allora Development Team
Date: 2025-07-17
"""

import os
import sys
import random
from datetime import datetime, timedelta
from decimal import Decimal

# Add the current directory to Python path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Flask app and models
from app import app, db, Category, Product, ProductImage, Banner, AdminUser

def create_admin_user():
    """Create an admin user for banner creation"""
    admin = AdminUser.query.filter_by(username='seeder_admin').first()
    if not admin:
        admin = AdminUser(
            username='seeder_admin',
            email='<EMAIL>',
            password='temp_password',
            first_name='Seeder',
            last_name='Admin',
            role='super_admin'
        )
        db.session.add(admin)
        db.session.commit()
    return admin

def create_categories():
    """Create hierarchical category structure for sustainable products"""
    print("🌱 Creating sustainable product categories...")
    
    categories_data = [
        # Main categories
        {
            'name': 'Home & Living',
            'slug': 'home-living',
            'description': 'Sustainable home essentials and living products',
            'level': 0,
            'sort_order': 1,
            'is_featured': True,
            'children': [
                {'name': 'Kitchen & Dining', 'slug': 'kitchen-dining', 'description': 'Sustainable kitchenware and dining essentials'},
                {'name': 'Bathroom Essentials', 'slug': 'bathroom-essentials', 'description': 'Natural bathroom and personal care items'},
                {'name': 'Home Decor', 'slug': 'home-decor', 'description': 'Sustainable home decoration and furnishing'},
                {'name': 'Cleaning Supplies', 'slug': 'cleaning-supplies', 'description': 'Natural and biodegradable cleaning products'},
            ]
        },
        {
            'name': 'Personal Care',
            'slug': 'personal-care',
            'description': 'Natural and organic personal care products',
            'level': 0,
            'sort_order': 2,
            'is_featured': True,
            'children': [
                {'name': 'Skincare', 'slug': 'skincare', 'description': 'Natural skincare and beauty products'},
                {'name': 'Hair Care', 'slug': 'hair-care', 'description': 'Organic hair care and styling products'},
                {'name': 'Oral Care', 'slug': 'oral-care', 'description': 'Natural dental and oral hygiene products'},
                {'name': 'Body Care', 'slug': 'body-care', 'description': 'Natural body care and wellness products'},
            ]
        },
        {
            'name': 'Fashion & Accessories',
            'slug': 'fashion-accessories',
            'description': 'Sustainable fashion and eco-friendly accessories',
            'level': 0,
            'sort_order': 3,
            'is_featured': True,
            'children': [
                {'name': 'Clothing', 'slug': 'clothing', 'description': 'Sustainable and ethically made clothing'},
                {'name': 'Bags & Accessories', 'slug': 'bags-accessories', 'description': 'Sustainable bags and fashion accessories'},
                {'name': 'Footwear', 'slug': 'footwear', 'description': 'Sustainable and comfortable footwear'},
                {'name': 'Jewelry', 'slug': 'jewelry', 'description': 'Ethically sourced and sustainable jewelry'},
            ]
        },
        {
            'name': 'Food & Beverages',
            'slug': 'food-beverages',
            'description': 'Organic and sustainable food products',
            'level': 0,
            'sort_order': 4,
            'is_featured': True,
            'children': [
                {'name': 'Organic Foods', 'slug': 'organic-foods', 'description': 'Certified organic food products'},
                {'name': 'Beverages', 'slug': 'beverages', 'description': 'Natural and organic beverages'},
                {'name': 'Snacks', 'slug': 'snacks', 'description': 'Healthy and sustainable snack options'},
                {'name': 'Pantry Staples', 'slug': 'pantry-staples', 'description': 'Organic pantry essentials and staples'},
            ]
        },
        {
            'name': 'Baby & Kids',
            'slug': 'baby-kids',
            'description': 'Safe and sustainable products for children',
            'level': 0,
            'sort_order': 5,
            'is_featured': False,
            'children': [
                {'name': 'Baby Care', 'slug': 'baby-care', 'description': 'Natural baby care and hygiene products'},
                {'name': 'Toys & Games', 'slug': 'toys-games', 'description': 'Sustainable and educational toys'},
                {'name': 'Kids Clothing', 'slug': 'kids-clothing', 'description': 'Organic and sustainable children\'s clothing'},
            ]
        }
    ]
    
    created_categories = {}
    
    # Create main categories first
    for cat_data in categories_data:
        category = Category.query.filter_by(slug=cat_data['slug']).first()
        if not category:
            category = Category(
                name=cat_data['name'],
                slug=cat_data['slug'],
                description=cat_data['description'],
                level=cat_data['level'],
                sort_order=cat_data['sort_order'],
                is_featured=cat_data['is_featured'],
                is_active=True,
                product_count=0
            )
            db.session.add(category)
            db.session.flush()  # Get the ID
        
        created_categories[cat_data['slug']] = category
        
        # Create subcategories
        for i, child_data in enumerate(cat_data.get('children', [])):
            child_category = Category.query.filter_by(slug=child_data['slug']).first()
            if not child_category:
                child_category = Category(
                    name=child_data['name'],
                    slug=child_data['slug'],
                    description=child_data['description'],
                    parent_id=category.id,
                    level=1,
                    sort_order=i + 1,
                    is_featured=False,
                    is_active=True,
                    product_count=0
                )
                db.session.add(child_category)
            
            created_categories[child_data['slug']] = child_category
    
    db.session.commit()
    print(f"✅ Created {len(created_categories)} categories")
    return created_categories

def get_sustainable_products_data():
    """Get data for 50 sustainable products with realistic names"""
    return [
        # Kitchen & Dining (12 products)
        {
            'name': 'Bamboo Cutting Board Set',
            'category': 'kitchen-dining',
            'brand': 'GreenChef',
            'price': 45.99,
            'description': 'Set of 3 bamboo cutting boards in different sizes. Naturally antimicrobial and knife-friendly.',
            'material': 'Bamboo',
            'sustainability_score': 95,
            'stock_quantity': 150,
            'weight': 1.2,
            'dimensions': '35x25x2 cm',
            'care_instructions': 'Hand wash with mild soap. Oil monthly to maintain.'
        },
        {
            'name': 'Stainless Steel Lunch Box',
            'category': 'kitchen-dining',
            'brand': 'PureLunch',
            'price': 32.50,
            'description': 'Leak-proof stainless steel lunch container with compartments. Perfect for meal prep.',
            'material': 'Stainless Steel',
            'sustainability_score': 90,
            'stock_quantity': 200,
            'weight': 0.8,
            'dimensions': '20x15x6 cm',
            'care_instructions': 'Dishwasher safe. Avoid abrasive cleaners.'
        },
        {
            'name': 'Glass Food Storage Jars',
            'category': 'kitchen-dining',
            'brand': 'ClearStore',
            'price': 28.99,
            'description': 'Set of 6 borosilicate glass jars with airtight bamboo lids. Perfect for pantry organization.',
            'material': 'Borosilicate Glass, Bamboo',
            'sustainability_score': 88,
            'stock_quantity': 120,
            'weight': 2.1,
            'dimensions': '10x10x15 cm each',
            'care_instructions': 'Dishwasher safe glass. Hand wash lids.'
        },
        {
            'name': 'Silicone Food Wraps',
            'category': 'kitchen-dining',
            'brand': 'WrapFresh',
            'price': 19.99,
            'description': 'Reusable silicone food wraps in assorted sizes. Replace plastic wrap and aluminum foil.',
            'material': 'Food-grade Silicone',
            'sustainability_score': 85,
            'stock_quantity': 180,
            'weight': 0.3,
            'dimensions': 'Various sizes',
            'care_instructions': 'Dishwasher safe or hand wash with warm soapy water.'
        },
        {
            'name': 'Copper Water Bottle',
            'category': 'kitchen-dining',
            'brand': 'PureCopper',
            'price': 42.00,
            'description': 'Handcrafted pure copper water bottle with natural antimicrobial properties.',
            'material': 'Pure Copper',
            'sustainability_score': 92,
            'stock_quantity': 95,
            'weight': 0.4,
            'dimensions': '26x7 cm',
            'care_instructions': 'Hand wash only. Clean with lemon and salt weekly.'
        },
        {
            'name': 'Wooden Spoon Set',
            'category': 'kitchen-dining',
            'brand': 'ForestCraft',
            'price': 24.50,
            'description': 'Set of 5 handcrafted wooden spoons from sustainably sourced hardwood.',
            'material': 'Sustainable Hardwood',
            'sustainability_score': 93,
            'stock_quantity': 140,
            'weight': 0.2,
            'dimensions': '25-30 cm length',
            'care_instructions': 'Hand wash and dry immediately. Oil monthly.'
        },
        {
            'name': 'Hemp Dish Towels',
            'category': 'kitchen-dining',
            'brand': 'NaturalClean',
            'price': 16.99,
            'description': 'Pack of 4 absorbent hemp dish towels. Naturally antimicrobial and quick-drying.',
            'material': 'Organic Hemp',
            'sustainability_score': 89,
            'stock_quantity': 220,
            'weight': 0.3,
            'dimensions': '40x30 cm',
            'care_instructions': 'Machine washable. Tumble dry low.'
        },
        {
            'name': 'Ceramic Dinnerware Set',
            'category': 'kitchen-dining',
            'brand': 'EarthTable',
            'price': 89.99,
            'description': '16-piece ceramic dinnerware set with natural glaze. Microwave and dishwasher safe.',
            'material': 'Natural Ceramic',
            'sustainability_score': 87,
            'stock_quantity': 75,
            'weight': 8.5,
            'dimensions': 'Various sizes',
            'care_instructions': 'Dishwasher and microwave safe. Avoid thermal shock.'
        },
        {
            'name': 'Compost Bin',
            'category': 'kitchen-dining',
            'brand': 'GreenCycle',
            'price': 35.00,
            'description': 'Countertop compost bin with charcoal filter. Reduces kitchen waste and odors.',
            'material': 'Stainless Steel, Bamboo',
            'sustainability_score': 94,
            'stock_quantity': 110,
            'weight': 1.1,
            'dimensions': '20x15x20 cm',
            'care_instructions': 'Hand wash bin. Replace filter every 6 months.'
        },
        {
            'name': 'Beeswax Food Wraps',
            'category': 'kitchen-dining',
            'brand': 'BeeNatural',
            'price': 22.50,
            'description': 'Set of organic cotton wraps infused with beeswax, jojoba oil, and tree resin.',
            'material': 'Organic Cotton, Beeswax',
            'sustainability_score': 96,
            'stock_quantity': 160,
            'weight': 0.2,
            'dimensions': 'Small, Medium, Large',
            'care_instructions': 'Rinse with cool water. Air dry. Refresh with heat if needed.'
        },
        {
            'name': 'Cast Iron Skillet',
            'category': 'kitchen-dining',
            'brand': 'IronCraft',
            'price': 65.00,
            'description': 'Pre-seasoned cast iron skillet. Naturally non-stick and improves with use.',
            'material': 'Cast Iron',
            'sustainability_score': 91,
            'stock_quantity': 85,
            'weight': 2.3,
            'dimensions': '25 cm diameter',
            'care_instructions': 'Hand wash, dry immediately, and oil lightly after each use.'
        },

        # Personal Care - Skincare (8 products)
        {
            'name': 'Rosehip Facial Oil',
            'category': 'skincare',
            'brand': 'PureGlow',
            'price': 28.99,
            'description': 'Cold-pressed organic rosehip oil rich in vitamins A and C. Anti-aging and hydrating.',
            'material': 'Organic Rosehip Oil',
            'sustainability_score': 94,
            'stock_quantity': 130,
            'weight': 0.1,
            'dimensions': '30ml bottle',
            'care_instructions': 'Store in cool, dark place. Use within 12 months of opening.'
        },
        {
            'name': 'Charcoal Face Mask',
            'category': 'skincare',
            'brand': 'DetoxSkin',
            'price': 18.50,
            'description': 'Activated charcoal clay mask with bentonite clay. Deep cleanses and purifies pores.',
            'material': 'Activated Charcoal, Bentonite Clay',
            'sustainability_score': 88,
            'stock_quantity': 175,
            'weight': 0.15,
            'dimensions': '50g jar',
            'care_instructions': 'Keep dry. Use clean hands or spatula to apply.'
        },
        {
            'name': 'Vitamin C Serum',
            'category': 'skincare',
            'brand': 'BrightSkin',
            'price': 34.99,
            'description': 'Stabilized vitamin C serum with hyaluronic acid. Brightens and evens skin tone.',
            'material': 'Vitamin C, Hyaluronic Acid',
            'sustainability_score': 86,
            'stock_quantity': 145,
            'weight': 0.1,
            'dimensions': '30ml bottle',
            'care_instructions': 'Refrigerate after opening. Use within 6 months.'
        },
        {
            'name': 'Aloe Vera Gel',
            'category': 'skincare',
            'brand': 'SoothingNature',
            'price': 15.99,
            'description': '99% pure aloe vera gel. Soothes, hydrates, and heals irritated skin naturally.',
            'material': 'Organic Aloe Vera',
            'sustainability_score': 95,
            'stock_quantity': 200,
            'weight': 0.2,
            'dimensions': '100ml tube',
            'care_instructions': 'Store in cool place. Refrigerate for extra cooling effect.'
        },
        {
            'name': 'Jojoba Oil Cleanser',
            'category': 'skincare',
            'brand': 'GentleClean',
            'price': 26.50,
            'description': 'Pure jojoba oil cleanser that dissolves makeup and impurities without stripping skin.',
            'material': 'Organic Jojoba Oil',
            'sustainability_score': 93,
            'stock_quantity': 155,
            'weight': 0.12,
            'dimensions': '50ml bottle',
            'care_instructions': 'Store at room temperature. Shake before use.'
        },
        {
            'name': 'Green Tea Toner',
            'category': 'skincare',
            'brand': 'HerbalBalance',
            'price': 21.99,
            'description': 'Alcohol-free toner with green tea extract and witch hazel. Balances and refreshes skin.',
            'material': 'Green Tea Extract, Witch Hazel',
            'sustainability_score': 89,
            'stock_quantity': 170,
            'weight': 0.15,
            'dimensions': '120ml bottle',
            'care_instructions': 'Store in cool, dry place. Use within 12 months.'
        },
        {
            'name': 'Shea Butter Moisturizer',
            'category': 'skincare',
            'brand': 'NourishSkin',
            'price': 24.99,
            'description': 'Rich moisturizer with organic shea butter and coconut oil. Deep hydration for dry skin.',
            'material': 'Organic Shea Butter, Coconut Oil',
            'sustainability_score': 92,
            'stock_quantity': 140,
            'weight': 0.18,
            'dimensions': '75ml jar',
            'care_instructions': 'Store at room temperature. Use clean hands to apply.'
        },
        {
            'name': 'Turmeric Face Scrub',
            'category': 'skincare',
            'brand': 'GoldenGlow',
            'price': 19.50,
            'description': 'Gentle exfoliating scrub with turmeric, oatmeal, and honey. Brightens and smooths skin.',
            'material': 'Turmeric, Oatmeal, Honey',
            'sustainability_score': 90,
            'stock_quantity': 125,
            'weight': 0.2,
            'dimensions': '100g jar',
            'care_instructions': 'Keep dry. Use 2-3 times per week for best results.'
        },

        # Hair Care (5 products)
        {
            'name': 'Argan Oil Shampoo',
            'category': 'hair-care',
            'brand': 'SilkyStrands',
            'price': 22.99,
            'description': 'Sulfate-free shampoo with organic argan oil. Nourishes and strengthens all hair types.',
            'material': 'Organic Argan Oil, Natural Surfactants',
            'sustainability_score': 88,
            'stock_quantity': 160,
            'weight': 0.3,
            'dimensions': '250ml bottle',
            'care_instructions': 'Store upright. Shake before use if separation occurs.'
        },
        {
            'name': 'Coconut Oil Hair Mask',
            'category': 'hair-care',
            'brand': 'TropicalHair',
            'price': 18.50,
            'description': 'Deep conditioning hair mask with virgin coconut oil and shea butter.',
            'material': 'Virgin Coconut Oil, Shea Butter',
            'sustainability_score': 94,
            'stock_quantity': 135,
            'weight': 0.25,
            'dimensions': '200ml jar',
            'care_instructions': 'Store at room temperature. May solidify in cold weather.'
        },
        {
            'name': 'Bamboo Hair Brush',
            'category': 'hair-care',
            'brand': 'NaturalBrush',
            'price': 16.99,
            'description': 'Bamboo paddle brush with natural boar bristles. Gentle detangling and scalp massage.',
            'material': 'Bamboo, Boar Bristles',
            'sustainability_score': 96,
            'stock_quantity': 180,
            'weight': 0.15,
            'dimensions': '23x8 cm',
            'care_instructions': 'Clean bristles weekly. Keep dry to prevent mold.'
        },
        {
            'name': 'Rosemary Hair Oil',
            'category': 'hair-care',
            'brand': 'HerbGarden',
            'price': 25.00,
            'description': 'Stimulating hair oil with rosemary and peppermint. Promotes healthy hair growth.',
            'material': 'Rosemary Oil, Peppermint Oil, Jojoba Oil',
            'sustainability_score': 91,
            'stock_quantity': 145,
            'weight': 0.1,
            'dimensions': '50ml bottle',
            'care_instructions': 'Store in dark place. Patch test before first use.'
        },
        {
            'name': 'Apple Cider Vinegar Rinse',
            'category': 'hair-care',
            'brand': 'ClarifyHair',
            'price': 14.99,
            'description': 'Clarifying hair rinse with organic apple cider vinegar. Removes buildup and adds shine.',
            'material': 'Organic Apple Cider Vinegar, Herbs',
            'sustainability_score': 87,
            'stock_quantity': 190,
            'weight': 0.25,
            'dimensions': '200ml bottle',
            'care_instructions': 'Dilute before use. Store in cool place.'
        },

        # Oral Care (3 products)
        {
            'name': 'Bamboo Toothbrush Set',
            'category': 'oral-care',
            'brand': 'SmileBamboo',
            'price': 12.99,
            'description': 'Pack of 4 bamboo toothbrushes with soft bristles. Biodegradable and plastic-free.',
            'material': 'Bamboo, Plant-based Bristles',
            'sustainability_score': 98,
            'stock_quantity': 250,
            'weight': 0.08,
            'dimensions': '19 cm length',
            'care_instructions': 'Rinse after use. Replace every 3 months.'
        },
        {
            'name': 'Charcoal Toothpaste',
            'category': 'oral-care',
            'brand': 'WhiteSmile',
            'price': 8.99,
            'description': 'Natural whitening toothpaste with activated charcoal and mint. Fluoride-free.',
            'material': 'Activated Charcoal, Natural Mint',
            'sustainability_score': 85,
            'stock_quantity': 220,
            'weight': 0.1,
            'dimensions': '100g tube',
            'care_instructions': 'Use pea-sized amount. Rinse thoroughly after brushing.'
        },
        {
            'name': 'Oil Pulling Mouthwash',
            'category': 'oral-care',
            'brand': 'AyurvedaOral',
            'price': 16.50,
            'description': 'Traditional oil pulling blend with coconut oil, sesame oil, and essential oils.',
            'material': 'Coconut Oil, Sesame Oil, Essential Oils',
            'sustainability_score': 92,
            'stock_quantity': 165,
            'weight': 0.2,
            'dimensions': '150ml bottle',
            'care_instructions': 'Swish for 10-15 minutes. Do not swallow. Spit in trash, not sink.'
        },

        # Fashion & Accessories (10 products)
        {
            'name': 'Organic Cotton T-Shirt',
            'category': 'clothing',
            'brand': 'PureWear',
            'price': 29.99,
            'description': 'Soft organic cotton t-shirt with fair trade certification. Available in multiple colors.',
            'material': 'Organic Cotton',
            'sustainability_score': 89,
            'stock_quantity': 200,
            'weight': 0.2,
            'dimensions': 'S, M, L, XL',
            'care_instructions': 'Machine wash cold. Tumble dry low or air dry.'
        },
        {
            'name': 'Hemp Canvas Tote Bag',
            'category': 'bags-accessories',
            'brand': 'CarryGreen',
            'price': 24.50,
            'description': 'Durable hemp canvas tote bag with reinforced handles. Perfect for shopping and daily use.',
            'material': 'Hemp Canvas',
            'sustainability_score': 94,
            'stock_quantity': 175,
            'weight': 0.3,
            'dimensions': '40x35x15 cm',
            'care_instructions': 'Spot clean or hand wash. Air dry only.'
        },
        {
            'name': 'Cork Wallet',
            'category': 'bags-accessories',
            'brand': 'NaturalStyle',
            'price': 35.00,
            'description': 'Slim bifold wallet made from sustainable cork leather. Water-resistant and durable.',
            'material': 'Cork Leather',
            'sustainability_score': 91,
            'stock_quantity': 120,
            'weight': 0.1,
            'dimensions': '11x8x1 cm',
            'care_instructions': 'Wipe clean with damp cloth. Avoid soaking.'
        },
        {
            'name': 'Recycled Wool Scarf',
            'category': 'clothing',
            'brand': 'WarmEarth',
            'price': 42.99,
            'description': 'Cozy scarf made from 100% recycled wool. Soft, warm, and available in earth tones.',
            'material': 'Recycled Wool',
            'sustainability_score': 87,
            'stock_quantity': 95,
            'weight': 0.25,
            'dimensions': '180x30 cm',
            'care_instructions': 'Dry clean only or gentle hand wash in cold water.'
        },
        {
            'name': 'Wooden Sunglasses',
            'category': 'bags-accessories',
            'brand': 'SunWood',
            'price': 68.00,
            'description': 'Handcrafted sunglasses with bamboo frames and polarized lenses. UV400 protection.',
            'material': 'Bamboo, Polarized Lenses',
            'sustainability_score': 88,
            'stock_quantity': 85,
            'weight': 0.05,
            'dimensions': 'One size fits most',
            'care_instructions': 'Clean lenses with microfiber cloth. Store in case.'
        },
        {
            'name': 'Linen Button-Up Shirt',
            'category': 'clothing',
            'brand': 'LinenLux',
            'price': 55.99,
            'description': 'Breathable linen shirt perfect for warm weather. Naturally antimicrobial and moisture-wicking.',
            'material': 'Organic Linen',
            'sustainability_score': 90,
            'stock_quantity': 110,
            'weight': 0.3,
            'dimensions': 'S, M, L, XL',
            'care_instructions': 'Machine wash cold. Iron while damp for best results.'
        },
        {
            'name': 'Recycled Plastic Sneakers',
            'category': 'footwear',
            'brand': 'StepGreen',
            'price': 89.99,
            'description': 'Comfortable sneakers made from recycled ocean plastic. Breathable and lightweight.',
            'material': 'Recycled Ocean Plastic, Organic Cotton',
            'sustainability_score': 93,
            'stock_quantity': 75,
            'weight': 0.8,
            'dimensions': 'US 6-12',
            'care_instructions': 'Remove insoles before washing. Air dry only.'
        },
        {
            'name': 'Silver Leaf Earrings',
            'category': 'jewelry',
            'brand': 'EarthJewels',
            'price': 45.50,
            'description': 'Delicate leaf-shaped earrings made from recycled sterling silver. Hypoallergenic.',
            'material': 'Recycled Sterling Silver',
            'sustainability_score': 86,
            'stock_quantity': 150,
            'weight': 0.01,
            'dimensions': '2.5 cm length',
            'care_instructions': 'Polish with silver cloth. Store in anti-tarnish pouch.'
        },
        {
            'name': 'Organic Cotton Socks',
            'category': 'clothing',
            'brand': 'ComfortFeet',
            'price': 18.99,
            'description': 'Pack of 3 organic cotton socks with cushioned sole. Breathable and moisture-wicking.',
            'material': 'Organic Cotton, Bamboo Fiber',
            'sustainability_score': 88,
            'stock_quantity': 220,
            'weight': 0.15,
            'dimensions': 'S, M, L',
            'care_instructions': 'Machine wash warm. Tumble dry medium heat.'
        },
        {
            'name': 'Jute Yoga Mat',
            'category': 'bags-accessories',
            'brand': 'ZenFlow',
            'price': 52.00,
            'description': 'Natural jute yoga mat with rubber backing. Non-slip and biodegradable.',
            'material': 'Jute, Natural Rubber',
            'sustainability_score': 95,
            'stock_quantity': 90,
            'weight': 2.1,
            'dimensions': '183x61x0.6 cm',
            'care_instructions': 'Wipe clean with damp cloth. Air dry completely.'
        },

        # Food & Beverages (8 products)
        {
            'name': 'Quinoa Grain Bowl Mix',
            'category': 'organic-foods',
            'brand': 'SuperGrains',
            'price': 12.99,
            'description': 'Organic quinoa blend with ancient grains. High protein and gluten-free.',
            'material': 'Organic Quinoa, Ancient Grains',
            'sustainability_score': 92,
            'stock_quantity': 180,
            'weight': 0.5,
            'dimensions': '500g package',
            'care_instructions': 'Store in cool, dry place. Use within 2 years.'
        },
        {
            'name': 'Cold-Pressed Juice',
            'category': 'beverages',
            'brand': 'FreshPress',
            'price': 8.50,
            'description': 'Cold-pressed green juice with kale, spinach, apple, and lemon. No added sugars.',
            'material': 'Organic Vegetables, Fruits',
            'sustainability_score': 85,
            'stock_quantity': 120,
            'weight': 0.5,
            'dimensions': '500ml bottle',
            'care_instructions': 'Keep refrigerated. Consume within 3 days of opening.'
        },
        {
            'name': 'Raw Almonds',
            'category': 'snacks',
            'brand': 'NutHarvest',
            'price': 15.99,
            'description': 'Organic raw almonds from sustainable farms. Rich in protein and healthy fats.',
            'material': 'Organic Raw Almonds',
            'sustainability_score': 88,
            'stock_quantity': 200,
            'weight': 0.45,
            'dimensions': '450g resealable bag',
            'care_instructions': 'Store in airtight container. Best consumed within 6 months.'
        },
        {
            'name': 'Herbal Tea Blend',
            'category': 'beverages',
            'brand': 'MountainHerbs',
            'price': 18.50,
            'description': 'Caffeine-free herbal tea blend with chamomile, lavender, and lemon balm.',
            'material': 'Organic Herbs',
            'sustainability_score': 94,
            'stock_quantity': 160,
            'weight': 0.1,
            'dimensions': '20 tea bags',
            'care_instructions': 'Store in dry place. Steep 5-7 minutes in hot water.'
        },
        {
            'name': 'Coconut Oil',
            'category': 'pantry-staples',
            'brand': 'TropicalPure',
            'price': 22.99,
            'description': 'Virgin coconut oil for cooking and beauty. Cold-pressed and unrefined.',
            'material': 'Virgin Coconut Oil',
            'sustainability_score': 90,
            'stock_quantity': 140,
            'weight': 0.5,
            'dimensions': '500ml jar',
            'care_instructions': 'Store at room temperature. May solidify below 24°C.'
        },
        {
            'name': 'Chia Seeds',
            'category': 'pantry-staples',
            'brand': 'SuperSeeds',
            'price': 14.50,
            'description': 'Organic chia seeds packed with omega-3 fatty acids and fiber.',
            'material': 'Organic Chia Seeds',
            'sustainability_score': 91,
            'stock_quantity': 185,
            'weight': 0.3,
            'dimensions': '300g resealable bag',
            'care_instructions': 'Store in cool, dry place. Use within 2 years.'
        },
        {
            'name': 'Dark Chocolate Bar',
            'category': 'snacks',
            'brand': 'PureChocolate',
            'price': 6.99,
            'description': '70% dark chocolate made from fair trade cacao. Rich and smooth flavor.',
            'material': 'Fair Trade Cacao, Organic Sugar',
            'sustainability_score': 87,
            'stock_quantity': 250,
            'weight': 0.1,
            'dimensions': '100g bar',
            'care_instructions': 'Store in cool, dry place below 18°C.'
        },
        {
            'name': 'Himalayan Pink Salt',
            'category': 'pantry-staples',
            'brand': 'MountainSalt',
            'price': 9.99,
            'description': 'Pure Himalayan pink salt with natural minerals. Unprocessed and chemical-free.',
            'material': 'Himalayan Pink Salt',
            'sustainability_score': 89,
            'stock_quantity': 220,
            'weight': 0.5,
            'dimensions': '500g shaker',
            'care_instructions': 'Store in dry place. Keep shaker holes clear.'
        },

        # Baby & Kids (5 products)
        {
            'name': 'Organic Baby Lotion',
            'category': 'baby-care',
            'brand': 'GentleBaby',
            'price': 16.99,
            'description': 'Gentle baby lotion with organic calendula and chamomile. Hypoallergenic and tear-free.',
            'material': 'Organic Calendula, Chamomile',
            'sustainability_score': 93,
            'stock_quantity': 145,
            'weight': 0.2,
            'dimensions': '200ml pump bottle',
            'care_instructions': 'Store at room temperature. Patch test before first use.'
        },
        {
            'name': 'Wooden Building Blocks',
            'category': 'toys-games',
            'brand': 'PlayNature',
            'price': 34.50,
            'description': 'Set of 50 natural wooden building blocks. Develops creativity and motor skills.',
            'material': 'Sustainable Hardwood',
            'sustainability_score': 96,
            'stock_quantity': 85,
            'weight': 1.2,
            'dimensions': 'Various sizes',
            'care_instructions': 'Wipe clean with damp cloth. Avoid soaking in water.'
        },
        {
            'name': 'Organic Cotton Onesie',
            'category': 'kids-clothing',
            'brand': 'SoftBaby',
            'price': 19.99,
            'description': 'Soft organic cotton onesie with snap closures. Available in natural colors.',
            'material': 'Organic Cotton',
            'sustainability_score': 90,
            'stock_quantity': 180,
            'weight': 0.1,
            'dimensions': '0-3M, 3-6M, 6-12M',
            'care_instructions': 'Machine wash warm. Tumble dry low heat.'
        },
        {
            'name': 'Natural Rubber Teether',
            'category': 'baby-care',
            'brand': 'SafeTeeth',
            'price': 12.50,
            'description': 'Natural rubber teething toy. BPA-free and safe for babies to chew.',
            'material': 'Natural Rubber',
            'sustainability_score': 94,
            'stock_quantity': 200,
            'weight': 0.05,
            'dimensions': '8x6 cm',
            'care_instructions': 'Hand wash with mild soap. Air dry completely.'
        },
        {
            'name': 'Wooden Puzzle Set',
            'category': 'toys-games',
            'brand': 'BrainGrow',
            'price': 28.99,
            'description': 'Set of 3 wooden puzzles for toddlers. Develops problem-solving and hand-eye coordination.',
            'material': 'Sustainable Wood, Non-toxic Paint',
            'sustainability_score': 92,
            'stock_quantity': 110,
            'weight': 0.8,
            'dimensions': '20x20 cm each',
            'care_instructions': 'Wipe clean with damp cloth. Check for loose pieces regularly.'
        }
    ]

def create_products(categories):
    """Create 50 sustainable products in the database"""
    print("🛍️ Creating sustainable products...")

    products_data = get_sustainable_products_data()
    created_products = []

    for product_data in products_data:
        # Check if product already exists
        existing_product = Product.query.filter_by(name=product_data['name']).first()
        if existing_product:
            created_products.append(existing_product)
            continue

        # Get category
        category_slug = product_data['category']
        category = categories.get(category_slug)
        if not category:
            print(f"⚠️ Category '{category_slug}' not found for product '{product_data['name']}'")
            continue

        # Generate SKU
        sku = f"{product_data['brand'][:3].upper()}-{product_data['name'][:3].upper()}-{random.randint(1000, 9999)}"

        # Create product
        product = Product(
            name=product_data['name'],
            price=product_data['price'],
            image=f"/static/images/products/{product_data['name'].lower().replace(' ', '_')}_main.jpg",
            sustainability_score=product_data['sustainability_score'],
            stock_quantity=product_data['stock_quantity'],
            description=product_data['description'],
            category=category.name,
            brand=product_data['brand'],
            sku=sku,
            weight=product_data['weight'],
            dimensions=product_data['dimensions'],
            material=product_data['material'],
            care_instructions=product_data['care_instructions'],
            average_rating=round(random.uniform(4.0, 5.0), 1),
            total_reviews=random.randint(5, 50),
            low_stock_threshold=20
        )

        db.session.add(product)
        created_products.append(product)

    db.session.commit()
    print(f"✅ Created {len(created_products)} products")
    return created_products

def create_product_images(products):
    """Create product images for each product"""
    print("📸 Creating product images...")

    created_images = []

    for product in products:
        # Check if images already exist for this product
        existing_images = ProductImage.query.filter_by(product_id=product.id).count()
        if existing_images > 0:
            continue

        # Create 2-4 images per product
        num_images = random.randint(2, 4)

        for i in range(num_images):
            image_url = f"/static/images/products/{product.name.lower().replace(' ', '_')}_{i+1}.jpg"
            alt_text = f"{product.name} - Image {i+1}"
            is_primary = (i == 0)  # First image is primary

            product_image = ProductImage(
                product_id=product.id,
                image_url=image_url,
                alt_text=alt_text,
                is_primary=is_primary,
                display_order=i + 1
            )

            db.session.add(product_image)
            created_images.append(product_image)

    db.session.commit()
    print(f"✅ Created {len(created_images)} product images")
    return created_images

def create_banners(admin_user):
    """Create promotional banners for the sustainable marketplace"""
    print("🎨 Creating promotional banners...")

    banners_data = [
        {
            'title': 'Welcome to Sustainable Living',
            'subtitle': 'Discover eco-friendly products that make a difference for you and the planet',
            'image_url': '/static/images/banners/sustainable_living_hero.jpg',
            'link_url': '/categories/home-living',
            'link_text': 'Shop Now',
            'position': 'home_hero',
            'display_order': 1
        },
        {
            'title': 'Natural Personal Care',
            'subtitle': 'Pamper yourself with organic skincare and beauty products',
            'image_url': '/static/images/banners/natural_skincare_banner.jpg',
            'link_url': '/categories/personal-care',
            'link_text': 'Explore Collection',
            'position': 'home_hero',
            'display_order': 2
        },
        {
            'title': 'Sustainable Fashion',
            'subtitle': 'Look good, feel good, do good with ethically made clothing',
            'image_url': '/static/images/banners/sustainable_fashion_banner.jpg',
            'link_url': '/categories/fashion-accessories',
            'link_text': 'Shop Fashion',
            'position': 'home_hero',
            'display_order': 3
        },
        {
            'title': 'Organic Food & Beverages',
            'subtitle': 'Nourish your body with pure, organic ingredients',
            'image_url': '/static/images/banners/organic_food_banner.jpg',
            'link_url': '/categories/food-beverages',
            'link_text': 'Shop Organic',
            'position': 'home_hero',
            'display_order': 4
        },
        {
            'title': 'Safe for Baby & Kids',
            'subtitle': 'Natural, non-toxic products for your little ones',
            'image_url': '/static/images/banners/baby_kids_banner.jpg',
            'link_url': '/categories/baby-kids',
            'link_text': 'Shop Baby',
            'position': 'category_top',
            'display_order': 1
        },
        {
            'title': 'Zero Waste Kitchen',
            'subtitle': 'Transform your kitchen with sustainable alternatives',
            'image_url': '/static/images/banners/zero_waste_kitchen.jpg',
            'link_url': '/categories/kitchen-dining',
            'link_text': 'Start Your Journey',
            'position': 'category_top',
            'display_order': 2
        }
    ]

    created_banners = []

    for banner_data in banners_data:
        # Check if banner already exists
        existing_banner = Banner.query.filter_by(title=banner_data['title']).first()
        if existing_banner:
            created_banners.append(existing_banner)
            continue

        banner = Banner(
            title=banner_data['title'],
            subtitle=banner_data['subtitle'],
            image_url=banner_data['image_url'],
            link_url=banner_data['link_url'],
            link_text=banner_data['link_text'],
            opens_new_tab=False,
            position=banner_data['position'],
            display_order=banner_data['display_order'],
            is_active=True,
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow() + timedelta(days=365),  # Active for 1 year
            created_by=admin_user.id
        )

        db.session.add(banner)
        created_banners.append(banner)

    db.session.commit()
    print(f"✅ Created {len(created_banners)} banners")
    return created_banners

def update_category_product_counts(categories, products):
    """Update product counts for each category"""
    print("📊 Updating category product counts...")

    for category in categories.values():
        # Count products in this category
        product_count = sum(1 for product in products if product.category == category.name)
        category.product_count = product_count

    db.session.commit()
    print("✅ Updated category product counts")

def main():
    """Main seeding function"""
    print("🌱 Starting database seeding for Allora Sustainable Marketplace")
    print("=" * 60)

    with app.app_context():
        try:
            # Create admin user first
            admin_user = create_admin_user()

            # Create categories
            categories = create_categories()

            # Create products
            products = create_products(categories)

            # Create product images
            create_product_images(products)

            # Create banners
            create_banners(admin_user)

            # Update category product counts
            update_category_product_counts(categories, products)

            print("=" * 60)
            print("🎉 Database seeding completed successfully!")
            print(f"📊 Summary:")
            print(f"   • Categories: {len(categories)}")
            print(f"   • Products: {len(products)}")
            print(f"   • Product Images: {len(products) * 3} (average)")
            print(f"   • Banners: 6")
            print("=" * 60)

        except Exception as e:
            print(f"❌ Error during seeding: {e}")
            db.session.rollback()
            raise

if __name__ == "__main__":
    main()
