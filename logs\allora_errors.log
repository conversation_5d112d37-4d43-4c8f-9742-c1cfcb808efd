2025-07-13 10:16:10 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:20:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:30:27 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:33:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:34:13 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:37:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:37:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:41:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:49:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:56:34 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:00:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:03:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:07:17 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:10:16 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:21:04 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:21:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:28:33 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:29:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:31:35 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:35:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:38:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:41:56 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:44:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:46:13 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:46:38 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:50:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:51:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:18:24 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:20:00 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:22:40 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:28:38 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:35:35 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:40:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:44:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:45:18 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:46:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:49:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 12:55:15 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 13:06:39 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:17:10 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:17:10 - allora - ERROR - Failed to blacklist token: value is not an integer or out of range
2025-07-13 14:30:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:31:42 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 14:47:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:12:48 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:21:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:22:06 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:22:37 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 15:23:27 - webhook_handlers - ERROR - Error parsing timestamp 2025-07-13T10:00:00Z: time data '2025-07-13T10:00:00Z' does not match format '%Y-%m-%d %H:%M:%S'
2025-07-13 15:31:39 - webhook_handlers - ERROR - Error parsing timestamp 2025-07-13T10:00:00Z: time data '2025-07-13T10:00:00Z' does not match format '%Y-%m-%d %H:%M:%S'
2025-07-13 16:13:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:44:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:47:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:52:28 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 16:54:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:02:36 - engineio.server - ERROR - The client is using an unsupported version of the Socket.IO or Engine.IO protocols (further occurrences of this error will be logged with level INFO)
2025-07-13 17:08:05 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:10:14 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:13:07 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:16:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:18:15 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 17:19:18 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:04:22 - allora - ERROR - Unhandled exception: MethodNotAllowed at None
2025-07-13 18:04:22 - allora - ERROR - Unhandled exception: 405 Method Not Allowed: The method is not allowed for the requested URL.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1458, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1440, in raise_routing_exception
    raise request.routing_exception  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\ctx.py", line 353, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025-07-13 18:04:37 - allora - ERROR - Unhandled exception: MethodNotAllowed at None
2025-07-13 18:04:37 - allora - ERROR - Unhandled exception: 405 Method Not Allowed: The method is not allowed for the requested URL.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1458, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1440, in raise_routing_exception
    raise request.routing_exception  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\ctx.py", line 353, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\werkzeug\routing\map.py", line 624, in match
    raise MethodNotAllowed(valid_methods=list(e.have_match_for)) from None
werkzeug.exceptions.MethodNotAllowed: 405 Method Not Allowed: The method is not allowed for the requested URL.
2025-07-13 18:15:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting basic metrics: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting carrier performance: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting daily volume: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting status distribution: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting recent exceptions: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:15:59 - tracking_dashboard - ERROR - Error getting shipment list: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting basic metrics: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting carrier performance: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting daily volume: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting status distribution: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:17:32 - tracking_dashboard - ERROR - Error getting recent exceptions: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 18:23:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:24:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 18:56:11 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:13:05 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:15:14 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:43:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:44:11 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:52:12 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:52:13 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting recent community posts: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting sustainability stories: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:23 - community_highlights_api - ERROR - Error getting featured eco brands: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:24 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 19:57:24 - community_highlights_api - ERROR - Error getting recent reviews: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:25:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:27:19 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:30:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:33:52 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:42:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:42:47 - webhook_handlers - ERROR - Error parsing timestamp None: strptime() argument 1 must be str, not None
2025-07-13 20:45:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting basic metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting carrier performance: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting daily volume: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting status distribution: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting recent exceptions: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - tracking_dashboard - ERROR - Error getting shipment list: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 20:45:10 - webhook_handlers - ERROR - Error parsing timestamp None: strptime() argument 1 must be str, not None
2025-07-13 21:05:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:05:10 - fulfillment_api - ERROR - No tracking number in webhook from blue_dart
2025-07-13 21:06:21 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:07:50 - webhook_handlers - ERROR - Error parsing timestamp None: strptime() argument 1 must be str, not None
2025-07-13 21:16:40 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:21:35 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:30:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:30:03 - allora - ERROR - Test error message
2025-07-13 21:30:03 - __main__ - ERROR - PERFORMANCE: failing_function failed after 0.000s - Test exception
2025-07-13 21:37:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:42:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:49:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:49:46 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:49:46 - sustainability_api - ERROR - Error getting green heroes: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:49:46 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:57:10 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:57:11 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:57:11 - sustainability_api - ERROR - Error getting green heroes: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:57:11 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:57:30 - sustainability_api - ERROR - Error getting green heroes: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:58:21 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 21:59:51 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 21:59:52 - sustainability_api - ERROR - Error getting sustainability metrics: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 21:59:52 - sustainability_api - ERROR - Error getting green heroes: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 21:59:52 - sustainability_api - ERROR - Error getting sustainability metrics: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 22:04:01 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 22:04:02 - sustainability_api - ERROR - Error getting sustainability metrics: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 22:04:02 - sustainability_api - ERROR - Error getting green heroes: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 22:04:02 - sustainability_api - ERROR - Error getting sustainability metrics: Working outside of request context.

This typically means that you attempted to use functionality that needed
an active HTTP request. Consult the documentation on testing for
information about how to avoid this problem.
2025-07-13 22:04:25 - sustainability_api - ERROR - Error getting green heroes: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 22:05:55 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 22:09:15 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 22:09:32 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 22:09:44 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 22:10:13 - sustainability_api - ERROR - Error getting sustainability metrics: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[SimpleSeller(seller)]'. Original exception was: Error creating backref 'approved_sellers' on relationship 'SimpleSeller.approver': property of that name exists on mapper 'Mapper[AdminUser(admin_user)]'
2025-07-13 22:13:53 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 22:19:51 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 22:24:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 22:25:40 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 17:31:01 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 17:31:02 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 17:33:30 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 17:33:30 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 17:36:12 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 17:36:12 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 17:37:31 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 17:37:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 17:42:14 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 17:42:14 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 18:02:03 - allora - ERROR - Failed to register fulfillment API: BLUE_DART
2025-07-15 18:02:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 18:10:06 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 18:59:17 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 18:59:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 19:01:39 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 19:43:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 20:11:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 21:00:59 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 21:01:59 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 21:03:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 21:47:05 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-15 23:15:06 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 00:02:00 - search_system.elasticsearch_search - ERROR - Advanced search with query builder error: BadRequestError(400, 'search_phase_execution_exception', 'Fielddata is disabled on [category] in [allora_products]. Text fields are not optimised for operations that require per-document field data like aggregations and sorting, so these operations are disabled by default. Please use a keyword field instead. Alternatively, set fielddata=true on [category] in order to load field data by uninverting the inverted index. Note that this can use significant memory.', Fielddata is disabled on [category] in [allora_products]. Text fields are not optimised for operations that require per-document field data like aggregations and sorting, so these operations are disabled by default. Please use a keyword field instead. Alternatively, set fielddata=true on [category] in order to load field data by uninverting the inverted index. Note that this can use significant memory.)
2025-07-16 00:02:01 - search_system.elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'Fielddata is disabled on [category] in [allora_products]. Text fields are not optimised for operations that require per-document field data like aggregations and sorting, so these operations are disabled by default. Please use a keyword field instead. Alternatively, set fielddata=true on [category] in order to load field data by uninverting the inverted index. Note that this can use significant memory.', Fielddata is disabled on [category] in [allora_products]. Text fields are not optimised for operations that require per-document field data like aggregations and sorting, so these operations are disabled by default. Please use a keyword field instead. Alternatively, set fielddata=true on [category] in order to load field data by uninverting the inverted index. Note that this can use significant memory.)
2025-07-16 00:02:02 - search_system.elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-16 00:11:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 00:25:55 - search_system.elasticsearch_search - ERROR - Advanced search with query builder error: BadRequestError(400, 'search_phase_execution_exception', 'Fielddata is disabled on [category] in [allora_products]. Text fields are not optimised for operations that require per-document field data like aggregations and sorting, so these operations are disabled by default. Please use a keyword field instead. Alternatively, set fielddata=true on [category] in order to load field data by uninverting the inverted index. Note that this can use significant memory.', Fielddata is disabled on [category] in [allora_products]. Text fields are not optimised for operations that require per-document field data like aggregations and sorting, so these operations are disabled by default. Please use a keyword field instead. Alternatively, set fielddata=true on [category] in order to load field data by uninverting the inverted index. Note that this can use significant memory.)
2025-07-16 00:25:56 - search_system.elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'Fielddata is disabled on [category] in [allora_products]. Text fields are not optimised for operations that require per-document field data like aggregations and sorting, so these operations are disabled by default. Please use a keyword field instead. Alternatively, set fielddata=true on [category] in order to load field data by uninverting the inverted index. Note that this can use significant memory.', Fielddata is disabled on [category] in [allora_products]. Text fields are not optimised for operations that require per-document field data like aggregations and sorting, so these operations are disabled by default. Please use a keyword field instead. Alternatively, set fielddata=true on [category] in order to load field data by uninverting the inverted index. Note that this can use significant memory.)
2025-07-16 00:25:56 - search_system.elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-16 16:02:04 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 16:13:42 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\integrations\stdlib.py", line 128, in getresponse
    return real_getresponse(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1368, in getresponse
    response.begin()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 317, in begin
    version, status, reason = self._read_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 286, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
http.client.RemoteDisconnected: Remote end closed connection without response

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\integrations\stdlib.py", line 128, in getresponse
    return real_getresponse(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1368, in getresponse
    response.begin()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 317, in begin
    version, status, reason = self._read_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 286, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
urllib3.exceptions.ProtocolError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-16 16:14:57 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:14:57 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:14:57 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:14:58 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:15:22 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:15:57 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:24:57 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:24:59 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:25:57 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:34:22 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:34:24 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:34:57 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:41:01 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:41:03 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:41:30 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:41:30 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:41:57 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:42:06 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:42:08 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:42:35 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:42:35 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:42:35 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:43:04 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 16:44:15 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 17:02:37 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\integrations\stdlib.py", line 128, in getresponse
    return real_getresponse(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1368, in getresponse
    response.begin()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 317, in begin
    version, status, reason = self._read_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 286, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
http.client.RemoteDisconnected: Remote end closed connection without response

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\integrations\stdlib.py", line 128, in getresponse
    return real_getresponse(self, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1368, in getresponse
    response.begin()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 317, in begin
    version, status, reason = self._read_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 286, in _read_status
    raise RemoteDisconnected("Remote end closed connection without"
urllib3.exceptions.ProtocolError: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-07-16 17:03:04 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:03:08 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:04:41 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:04:43 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:10 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:10 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:12 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:15 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:17 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:19 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:20 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:23 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:25 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:27 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:29 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:29 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:29 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:29 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:29 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:31 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:33 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:05:35 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:06:10 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:08:54 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:08:56 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:23 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:23 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:25 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:27 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:29 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:32 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:33 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:37 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:38 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:40 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:42 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:42 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:42 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:42 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:42 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:44 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:46 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:09:48 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 17:10:23 - sentry_sdk.errors - ERROR - Internal error in sentry_sdk
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 565, in send_envelope_wrapper
    self._send_envelope(envelope)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 499, in _send_envelope
    self._send_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 351, in _send_request
    response = self._request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sentry_sdk\transport.py", line 716, in _request
    return self._pool.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 143, in request
    return self.request_encode_body(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\_request_methods.py", line 278, in request_encode_body
    return self.urlopen(method, url, **extra_kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\poolmanager.py", line 459, in urlopen
    response = conn.urlopen(method, u.request_uri, **kw)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 464, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 1093, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 790, in connect
    sock_and_verified = _ssl_wrap_socket_and_match_hostname(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 922, in _ssl_wrap_socket_and_match_hostname
    context = create_urllib3_context(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\ssl_.py", line 303, in create_urllib3_context
    context.minimum_version = TLSVersion.TLSv1_2
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 603, in minimum_version
    super(SSLContext, SSLContext).minimum_version.__set__(self, value)
  [Previous line repeated 487 more times]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\ssl.py", line 601, in minimum_version
    if value == TLSVersion.SSLv3:
RecursionError: maximum recursion depth exceeded while calling a Python object
2025-07-16 18:42:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 18:46:10 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:02:45 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:19:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:21:28 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:21:31 - sentry_sdk.errors - ERROR - flush timed out, dropped 2 events
2025-07-16 19:23:11 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:28:04 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:30:11 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:33:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:40:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:42:16 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:44:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:46:54 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:52:05 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:53:16 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:54:42 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:55:51 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:56:24 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-16 19:57:20 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 11:05:22 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 12:44:08 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 12:45:39 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 12:46:11 - allora - ERROR - Feature extraction failed: broken data stream when reading image file
2025-07-17 16:19:14 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 16:20:22 - __main__ - ERROR - ❌ Error clearing table 'hashtag': (mysql.connector.errors.IntegrityError) 1451 (23000): Cannot delete or update a parent row: a foreign key constraint fails (`allora_db`.`post_hashtag`, CONSTRAINT `post_hashtag_ibfk_2` FOREIGN KEY (`hashtag_id`) REFERENCES `hashtag` (`id`))
[SQL: DELETE FROM hashtag]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-17 16:20:22 - __main__ - ERROR - ❌ Error clearing table 'shipping_zone': (mysql.connector.errors.IntegrityError) 1451 (23000): Cannot delete or update a parent row: a foreign key constraint fails (`allora_db`.`shipping_method`, CONSTRAINT `shipping_method_ibfk_1` FOREIGN KEY (`zone_id`) REFERENCES `shipping_zone` (`id`))
[SQL: DELETE FROM shipping_zone]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-17 16:20:22 - __main__ - ERROR - ❌ Error clearing table 'sellers': (mysql.connector.errors.IntegrityError) 1451 (23000): Cannot delete or update a parent row: a foreign key constraint fails (`allora_db`.`products`, CONSTRAINT `products_ibfk_1` FOREIGN KEY (`seller_id`) REFERENCES `sellers` (`id`))
[SQL: DELETE FROM sellers]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-17 16:20:22 - __main__ - ERROR - ❌ Error clearing table 'orders': (mysql.connector.errors.IntegrityError) 1451 (23000): Cannot delete or update a parent row: a foreign key constraint fails (`allora_db`.`order_item`, CONSTRAINT `order_item_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`))
[SQL: DELETE FROM orders]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-17 16:20:23 - __main__ - ERROR - ❌ 4 errors occurred:
2025-07-17 16:20:23 - __main__ - ERROR -    ❌ Error clearing table 'hashtag': (mysql.connector.errors.IntegrityError) 1451 (23000): Cannot delete or update a parent row: a foreign key constraint fails (`allora_db`.`post_hashtag`, CONSTRAINT `post_hashtag_ibfk_2` FOREIGN KEY (`hashtag_id`) REFERENCES `hashtag` (`id`))
[SQL: DELETE FROM hashtag]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-17 16:20:23 - __main__ - ERROR -    ❌ Error clearing table 'shipping_zone': (mysql.connector.errors.IntegrityError) 1451 (23000): Cannot delete or update a parent row: a foreign key constraint fails (`allora_db`.`shipping_method`, CONSTRAINT `shipping_method_ibfk_1` FOREIGN KEY (`zone_id`) REFERENCES `shipping_zone` (`id`))
[SQL: DELETE FROM shipping_zone]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-17 16:20:23 - __main__ - ERROR -    ❌ Error clearing table 'sellers': (mysql.connector.errors.IntegrityError) 1451 (23000): Cannot delete or update a parent row: a foreign key constraint fails (`allora_db`.`products`, CONSTRAINT `products_ibfk_1` FOREIGN KEY (`seller_id`) REFERENCES `sellers` (`id`))
[SQL: DELETE FROM sellers]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-17 16:20:23 - __main__ - ERROR -    ❌ Error clearing table 'orders': (mysql.connector.errors.IntegrityError) 1451 (23000): Cannot delete or update a parent row: a foreign key constraint fails (`allora_db`.`order_item`, CONSTRAINT `order_item_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`))
[SQL: DELETE FROM orders]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-07-17 16:20:23 - __main__ - ERROR - ❌ Database clearing completed with errors
2025-07-17 16:22:03 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 16:22:51 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 16:23:34 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 16:26:36 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 16:26:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 16:29:06 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 16:43:21 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 16:46:33 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 16:58:55 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 17:11:55 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 17:16:44 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 17:18:39 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 17:21:26 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 17:24:42 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 17:25:33 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 17:30:00 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 17:39:15 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 17:54:27 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 17:57:25 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 18:07:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 18:10:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 18:14:04 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 18:16:39 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 18:22:34 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 20:16:01 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'c:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-17 21:03:17 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
